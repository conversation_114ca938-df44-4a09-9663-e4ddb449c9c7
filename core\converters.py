#!/usr/bin/env python3
"""
TMT檔案轉換器核心模組
7步驟統一處理架構

📋 總覽：7個主要步驟
步驟1: 檔案讀取與預處理
步驟2: 檔案解壓縮（如需要）
步驟3: SPD轉CSV（如需要）
步驟4: 格式檢測與分流
步驟5: CTA格式轉TMT格式（如需要）
步驟6: 數據填充與統一收集
步驟7: Summary工作表生成

包含：
- FullConverter: 完整Excel轉換器（執行步驟1-7）
- SummaryCSVConverter: 快速Summary轉換器（執行步驟6-7）
"""

import pandas as pd
from utils.file_utils import get_file_handler
from utils.excel_utils import get_excel_handler
from core.processors import get_cta8280_processor, get_fill_empty_processor, get_device2bin_processor
from config.settings import (
    TesterType, DATA_MARKERS, OUTPUT_SUFFIX_FULL, OUTPUT_SUFFIX_SPD_FULL
)
import openpyxl
import os

class BaseConverter:
    """基礎轉換器類別 - 7步驟統一處理架構的基礎類別

    提供：
    - 格式檢測邏輯（步驟4）
    - 統一處理管道框架
    - 條件性步驟執行控制
    """
    
    def __init__(self):
        self.file_handler = get_file_handler()
        self.excel_handler = get_excel_handler()
        self.my_tester_type = TesterType.UNKNOWN
        # 將處理器移到基類，讓所有轉換器都能使用
        self.cta8280_processor = get_cta8280_processor()
        self.fill_empty_processor = get_fill_empty_processor()
        self.device2bin_processor = get_device2bin_processor()
    
    def detect_tester_type(self, df, file_path=None):
        """檢測測試儀類型"""
        # 步驟4：按照新的檢測邏輯
        # 1. 先檢查A1是否有"Spreadsheet"（TMT格式）
        try:
            a1_value = str(df.iloc[0, 0]) if pd.notna(df.iloc[0, 0]) else ""
            if "Spreadsheet" in a1_value:
                self.my_tester_type = TesterType.TMT
                self.file_handler.log_message("檢測到TMT測試儀格式（通過A1=Spreadsheet）")
                return True
        except (IndexError, KeyError):
            pass

        # 2. 如果沒有，在A列找[Data]字樣（CTA格式）
        for i, val in enumerate(df.iloc[:, 0]):
            val_str = str(val) if pd.notna(val) else ""
            if val_str == DATA_MARKERS['DATA']:
                self.my_tester_type = TesterType.CTA8280
                self.file_handler.log_message("檢測到CTA8280測試儀格式（通過[Data]標記）")
                return True

        # 3. 如果都沒有，則是其他格式
        self.my_tester_type = TesterType.OTHER
        self.file_handler.log_message("檢測到其他格式（未找到Spreadsheet或[Data]標記）")
        return True  # 改為True，讓其他格式也能繼續處理

    def _unified_processing_pipeline(self, df, working_file, create_summary, original_filename, output_format="excel"):
        """統一的7步驟處理管道：讓所有格式都走相同的標準化流程

        按照程式步驟流程圖.md執行：
        步驟1: 檔案讀取與預處理（已在上層完成）
        步驟2: 檔案解壓縮（如需要，已在上層完成）
        步驟3: SPD轉CSV（如需要，已在上層完成）
        步驟4: 格式檢測與分流（已完成）
        步驟5: CTA格式轉TMT格式（條件性執行）
        步驟6: 數據填充與統一收集
        步驟7: Summary工作表生成
        """
        self.file_handler.log_message("=== 開始7步驟統一處理管道 ===")

        # 步驟1-3: 已在上層完成（檔案讀取與預處理、檔案解壓縮、SPD轉CSV）

        # 步驟4: 格式檢測與分流已完成，根據類型決定是否需要步驟5
        if self.my_tester_type == TesterType.TMT:
            self.file_handler.log_message("步驟4: 檢測到TMT格式，直接進入步驟6")
            # TMT格式直接處理，假設整個DataFrame就是Data11數據
            wb, ws_sum, ws_data11 = self._create_tmt_workbook(df)

        elif self.my_tester_type == TesterType.CTA8280:
            self.file_handler.log_message("步驟4: 檢測到CTA格式，需要執行步驟5轉換")
            # 步驟5: CTA格式轉換為TMT格式
            wb, ws_sum, ws_data11 = self._convert_cta_to_tmt(df)

        else:
            self.file_handler.log_message("步驟4: 檢測到其他格式，嘗試通用處理")
            # 其他格式使用通用處理
            wb, ws_sum, ws_data11 = self._create_generic_workbook(df)

        # 步驟6: 數據填充與統一收集
        self.file_handler.log_message("🚀 步驟6: 開始數據填充與統一收集...")
        data_summary = self._apply_step6_processing(ws_data11, ws_sum, output_format)

        # 步驟7: Summary工作表生成（使用步驟6的統一數據）
        if create_summary:
            self.file_handler.log_message("🚀 步驟7: 開始Summary工作表生成...")
            self._apply_step7_processing(ws_data11, original_filename, data_summary)

        self.file_handler.log_message("=== 統一處理管道完成 ===")
        return wb, ws_sum, ws_data11

    def _create_tmt_workbook(self, df):
        """為TMT格式創建工作簿（A1=Spreadsheet的格式）"""
        self.file_handler.log_message("創建TMT格式工作簿...")

        # TMT格式假設整個DataFrame就是完整的數據，不需要分割
        from openpyxl import Workbook
        wb = Workbook()
        ws_data11 = wb.active
        ws_data11.title = "Data11"

        # 將DataFrame寫入工作表
        self.excel_handler.write_dataframe_to_worksheet(df, ws_data11)

        # 步驟5：在9A位置標記原始格式為TMT
        self.file_handler.log_message("步驟5：在9A位置標記原始格式為TMT...")
        ws_data11.cell(9, 1).value = "TMT"

        # TMT格式不需要sum工作表，設為None
        self.file_handler.log_message("TMT格式不需要sum分頁")
        ws_sum = None

        self.file_handler.log_message(f"TMT工作簿創建完成：僅Data11工作表 {len(df)}行，無sum分頁")
        return wb, ws_sum, ws_data11

    def _convert_cta_to_tmt(self, df):
        """步驟5: CTA格式轉換為TMT格式"""
        self.file_handler.log_message("執行CTA到TMT格式轉換...")

        # 找到[Data]標記進行數據分割
        data_row, qa_data_row = self.excel_handler.find_data_markers(df)
        if data_row is None:
            self.file_handler.log_message("警告：CTA格式中未找到[Data]標記，使用整個DataFrame")
            data_row = 0

        # 分割數據
        sum_data = df.iloc[:data_row] if data_row > 0 else pd.DataFrame()
        data_end = qa_data_row if qa_data_row is not None else len(df)
        data11_data = df.iloc[data_row:data_end]
        qa_data = df.iloc[qa_data_row+1:] if qa_data_row is not None else None

        # 創建工作簿
        wb, ws_sum, ws_data11 = self.excel_handler.create_workbook_with_sheets(
            sum_data, data11_data, qa_data)

        # 應用CTA8280格式處理（步驟5的核心：a.刪除前面行數 b.交換 c.插入AB列和6行）
        self.file_handler.log_message("應用CTA8280轉換處理...")
        self.cta8280_processor.apply_format(ws_data11, ws_sum)

        # 步驟5：在9A位置標記原始格式為CTA
        self.file_handler.log_message("步驟5：在9A位置標記原始格式為CTA...")
        ws_data11.cell(9, 1).value = "CTA"

        return wb, ws_sum, ws_data11

    def _create_generic_workbook(self, df):
        """為其他格式創建通用工作簿"""
        self.file_handler.log_message("創建通用格式工作簿...")

        # 其他格式使用簡單處理
        from openpyxl import Workbook
        wb = Workbook()
        ws_data11 = wb.active
        ws_data11.title = "Data11"

        # 將DataFrame寫入工作表
        self.excel_handler.write_dataframe_to_worksheet(df, ws_data11)

        # 步驟5：在9A位置標記原始格式為OTHER
        self.file_handler.log_message("步驟5：在9A位置標記原始格式為OTHER...")
        ws_data11.cell(9, 1).value = "OTHER"

        # 創建空的sum工作表
        ws_sum = wb.create_sheet("sum")

        return wb, ws_sum, ws_data11

    def _apply_step6_processing(self, ws_data11, ws_sum, output_format="excel"):
        """步驟6: 數據填充與統一收集

        按照程式步驟流程圖.md執行：
        - 6.1 統一數據收集（項目、設備、Site、限制值）
        - 6.2 填充測試項目名稱和編號
        - 6.3 填充Min/Max值
        - 6.4 數據驗證與整理

        Returns:
            dict: 統一的數據摘要結構（data_summary），供步驟7使用
        """
        self.file_handler.log_message("🔄 步驟6.1: 開始統一數據收集...")
        self.file_handler.log_message("  - 6.1.1 收集測試項目數據（項目數量、名稱、位置）")
        self.file_handler.log_message("  - 6.1.2 收集設備數據（設備數量、行位置、Bin值）")
        self.file_handler.log_message("  - 6.1.3 收集Site信息（Site列位置、Site數據、統計）")
        self.file_handler.log_message("  - 6.1.4 收集限制值（Max/Min值）")
        self.file_handler.log_message("  - 6.1.5 設置第6行項目編號")

        # 應用FillEmptyItemName處理（統一數據收集和填充）
        self.file_handler.log_message("應用FillEmptyItemName處理（統一優化）...")
        data_summary = self.fill_empty_processor.apply_processing(ws_data11)

        # 應用Device2BinControl處理（使用統一數據，計算測項排序，fail填色）
        self.file_handler.log_message("應用Device2BinControl處理...")
        # 注意：這裡不創建summary，因為步驟7會單獨處理
        self.device2bin_processor.apply_processing(
            ws_data11,
            create_summary=False,
            output_format=output_format,
            data_summary=data_summary
        )

        return data_summary

    def _apply_step7_processing(self, ws_data11, original_filename, data_summary):
        """步驟7: Summary工作表生成（使用步驟6的統一數據）

        按照程式步驟流程圖.md執行：
        - 7.1 VBA核心分析（統一數據版）
        - 7.2 創建增強Summary工作表
          - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
          - 7.2.2 添加原始檔案超連結（C1）
          - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
          - 7.2.4 填充F列開始的Site統計（數量和百分比）
          - 7.2.5 填充測試項目Bin數據
          - 7.2.6 設置AutoFilter和排序

        Args:
            ws_data11: 工作表
            original_filename: 原始檔案名
            data_summary: 步驟6傳遞的統一數據摘要
        """
        self.file_handler.log_message("🔄 步驟7.1: 開始VBA核心分析（統一數據版）...")
        self.file_handler.log_message("  - 7.1.1 收集原始Bin值")
        self.file_handler.log_message("  - 7.1.2 執行設備分析（測試項目失敗檢測）")
        self.file_handler.log_message("  - 7.1.3 計算Bin統計")
        self.file_handler.log_message("  - 7.1.4 應用染色邏輯（Excel格式）")
        self.file_handler.log_message("🔄 步驟7.2: 開始創建增強Summary工作表...")
        self.file_handler.log_message("  - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）")
        self.file_handler.log_message("  - 7.2.2 添加原始檔案超連結（C1）")
        self.file_handler.log_message("  - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）")
        self.file_handler.log_message("  - 7.2.4 填充F列開始的Site統計（數量和百分比）")
        self.file_handler.log_message("  - 7.2.5 填充測試項目Bin數據")
        self.file_handler.log_message("  - 7.2.6 設置AutoFilter和排序")

        # 應用Device2BinControl處理來創建Summary（使用統一數據）
        self.device2bin_processor.apply_processing(
            ws_data11,
            create_summary=True,
            original_filename=original_filename,
            data_summary=data_summary
        )

class FullConverter(BaseConverter):
    """完整轉換器 - 執行完整7步驟處理，產生Excel格式

    功能：
    - 執行步驟1-7完整轉換流程
    - 支援TMT/CTA8280格式自動檢測
    - 統一數據收集優化，避免重複計算
    - 生成包含Summary工作表的完整Excel檔案
    """
    
    def __init__(self):
        super().__init__()
    
    def convert(self, input_file, create_summary=False, output_dir=None, original_zip_file=None):
        """執行完整7步驟轉換流程

        轉換步驟：
        步驟1: 檔案讀取與預處理
        步驟2-3: SPD解壓縮與轉CSV（條件性）
        步驟4: 格式檢測與分流
        步驟5: CTA→TMT格式轉換（條件性）
        步驟6: 統一數據收集與填充
        步驟7: Summary工作表生成（條件性）

        Args:
            input_file: 輸入檔案路徑
            create_summary: 是否創建Summary工作表
            output_dir: 輸出目錄路徑（可選）
            original_zip_file: 原始ZIP檔案路徑（如果是從ZIP解壓的）
        """
        self.file_handler.log_message("開始完整轉換...")

        # 統一處理所有檔案類型（SPD、CSV等）
        temp_csv_file = None  # 初始化變數
        working_file = input_file

        try:
            # 讀取檔案（現在都是CSV格式）
            df = self.file_handler.read_csv_file(working_file)
            if df is None:
                return False, None

            # 統一處理流程：所有格式都走相同的步驟
            if not self.detect_tester_type(df, working_file):
                return False, None

            # 根據格式類型進行統一處理
            # 如果有原始ZIP檔案，使用ZIP檔案路徑作為原始檔案名稱
            original_filename = original_zip_file if original_zip_file else input_file
            wb, ws_sum, ws_data11 = self._unified_processing_pipeline(df, working_file, create_summary, original_filename)

            # 應用字體格式
            self.excel_handler.apply_font_formatting(wb)

            # 優化9: 使用優化的保存方法
            if self.file_handler.is_spd_file(input_file):
                output_file = self.file_handler.generate_output_filename(input_file, OUTPUT_SUFFIX_SPD_FULL, output_dir)
            else:
                output_file = self.file_handler.generate_output_filename(input_file, OUTPUT_SUFFIX_FULL, output_dir)

            # 使用優化的保存方法
            save_success = self.excel_handler.save_workbook_optimized(wb, output_file)
            if not save_success:
                raise Exception("Excel檔案保存失敗")

            self.file_handler.log_message(f"完整轉換完成！輸出文件: {output_file}")
            return True, output_file

        finally:
            # 清理臨時CSV檔案
            if temp_csv_file:
                self.file_handler.cleanup_temp_file(temp_csv_file)



def get_full_converter():
    """獲取完整轉換器實例"""
    return FullConverter()

class SummaryCSVConverter(BaseConverter):
    """Summary CSV轉換器 - 專門產生Summary工作表並另存為CSV"""

    def __init__(self):
        super().__init__()

    def convert(self, input_file, original_zip_file=None, output_dir=None):
        """執行Summary CSV轉換

        Args:
            input_file: 輸入檔案路徑（可能是解壓後的檔案）
            original_zip_file: 原始ZIP檔案路徑（如果是從ZIP解壓的）
            output_dir: 輸出目錄路徑（可選）
        """
        self.file_handler.log_message("開始Summary CSV轉換...")

        try:
            # 步驟1: 讀取CSV文件
            df = self.file_handler.read_csv_file(input_file)
            if df is None:
                return False, None

            # 步驟2: 檢測測試儀類型（SPD文件特殊處理）
            if self.file_handler.is_spd_file(input_file):
                self.file_handler.log_message("檢測到SPD文件，使用SPD專用處理邏輯")
                # SPD文件直接設置為支持的類型，跳過標準檢測
                self.my_tester_type = TesterType.CTA8280  # SPD文件通常是CTA8280格式
            else:
                # 標準文件使用標準檢測
                if not self.detect_tester_type(df, input_file):
                    self.file_handler.log_message("不支持的文件格式")
                    return False, None

            # 步驟3: 創建臨時Excel文件進行完整處理
            temp_excel_file = self._create_temp_excel_with_summary(df, input_file)
            if not temp_excel_file:
                return False, None

            # 步驟4: 從Excel文件中提取Summary工作表並轉為CSV
            # 如果有原始ZIP檔案，使用ZIP檔案路徑作為連結
            link_file = original_zip_file if original_zip_file else input_file
            summary_csv_file = self._extract_summary_to_csv(temp_excel_file, link_file, output_dir)

            # 步驟5: 清理臨時文件
            try:
                if os.path.exists(temp_excel_file):
                    os.remove(temp_excel_file)
            except Exception as e:
                self.file_handler.log_message(f"清理臨時文件時出錯: {e}")

            if summary_csv_file:
                self.file_handler.log_message(f"Summary CSV轉換完成！輸出文件: {summary_csv_file}")
                return True, summary_csv_file
            else:
                return False, None

        except Exception as e:
            self.file_handler.log_message(f"Summary CSV轉換失敗: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return False, None

    def _create_temp_excel_with_summary(self, df, input_file):
        """創建包含Summary的臨時Excel文件"""
        try:
            # 生成臨時文件名（支持SPD和CSV文件）
            if self.file_handler.is_spd_file(input_file):
                temp_file = input_file.replace('.spd', '_temp_summary.xlsx')
            else:
                temp_file = input_file.replace('.csv', '_temp_summary.xlsx')

            self.file_handler.log_message("使用完整轉換邏輯創建臨時文件...")

            # 使用FullConverter來處理，確保所有步驟都正確執行
            from core.converters import FullConverter
            full_converter = FullConverter()

            # 執行完整轉換到臨時文件
            success, full_output = full_converter.convert(input_file, create_summary=True)

            if success and full_output:
                # 將完整轉換的結果複製為臨時文件
                import shutil
                shutil.copy2(full_output, temp_file)

                self.file_handler.log_message(f"使用完整轉換創建臨時Excel文件: {temp_file}")

                # 清理完整轉換的輸出文件（我們只需要臨時文件）
                try:
                    if os.path.exists(full_output):
                        os.remove(full_output)
                        self.file_handler.log_message(f"清理完整轉換輸出文件: {full_output}")
                except Exception as cleanup_e:
                    self.file_handler.log_message(f"清理完整轉換輸出文件時出錯: {cleanup_e}")

                return temp_file
            else:
                self.file_handler.log_message("完整轉換失敗，無法創建臨時文件")
                return None

        except Exception as e:
            self.file_handler.log_message(f"創建臨時Excel文件失敗: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return None

    def _extract_summary_to_csv(self, excel_file, original_input_file, output_dir=None):
        """從Excel文件中提取Summary工作表並轉為CSV"""
        try:
            # 打開Excel文件
            wb = openpyxl.load_workbook(excel_file)

            if 'Summary' not in wb.sheetnames:
                self.file_handler.log_message("Excel文件中沒有找到Summary工作表")
                wb.close()
                return None

            # 獲取Summary工作表
            summary_ws = wb['Summary']
            self.file_handler.log_message(f"Summary工作表大小: {summary_ws.max_row} x {summary_ws.max_column}")

            # 完整CSV結構：按Excel Summary工作表的原始順序輸出
            # 不使用標題行，直接輸出所有數據行

            # 提取所有數據行（第1-8行）
            all_data = []
            max_cols = summary_ws.max_column

            self.file_handler.log_message(f"提取Summary工作表所有數據（第1-{summary_ws.max_row}行）...")
            for row in range(1, summary_ws.max_row + 1):
                row_data = []
                has_data = False

                for col in range(1, max_cols + 1):
                    cell_value = summary_ws.cell(row, col).value
                    if cell_value is not None:
                        row_data.append(str(cell_value))
                        has_data = True
                    else:
                        row_data.append("")

                if has_data:
                    all_data.append(row_data)
                    # 移除詳細的Summary行信息日誌

            # 修正C2位置的原始檔案路徑
            if original_input_file and len(all_data) >= 2:
                # 確保第2行有足夠的列數
                while len(all_data[1]) < 3:
                    all_data[1].append("")

                # 在C2位置（第2行第3列）放置原始檔案的完整路徑
                import os
                full_path = os.path.abspath(original_input_file)
                all_data[1][2] = full_path
                self.file_handler.log_message(f"在Summary CSV的C2位置設置原始檔案路徑: {full_path}")

            # 不使用標題行，直接輸出數據
            # 使用第一行作為假標題，但實際上所有行都是數據
            headers = all_data[0] if all_data else []
            data = all_data[1:] if all_data else []  # 跳過第一行，避免重複



            wb.close()

            # 創建DataFrame
            if data and headers:
                df_summary = pd.DataFrame(data, columns=headers)

                # 生成輸出文件名
                output_file = self._generate_summary_csv_filename(original_input_file, output_dir)

                # 保存為CSV
                df_summary.to_csv(output_file, index=False, encoding='utf-8-sig')

                self.file_handler.log_message(f"Summary工作表已轉換為CSV: {output_file}")
                self.file_handler.log_message(f"Summary數據: {len(data)}行 × {len(headers)}列")
                self.file_handler.log_message(f"標題: {headers}")

                return output_file
            else:
                self.file_handler.log_message(f"Summary工作表中沒有找到有效數據。標題: {headers}, 數據行數: {len(data)}")
                return None

        except Exception as e:
            self.file_handler.log_message(f"提取Summary工作表失敗: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return None

    def _generate_summary_csv_filename(self, input_file, output_dir=None):
        """生成Summary CSV文件名"""
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        filename = f"{base_name}_summary.csv"

        if output_dir:
            return os.path.join(output_dir, filename)
        else:
            # 如果沒有指定輸出目錄，使用輸入檔案的目錄
            input_dir = os.path.dirname(input_file)
            return os.path.join(input_dir, filename)

    def _create_basic_summary_if_needed(self, wb, ws_data11):
        """如果沒有Summary工作表，創建一個基本的Summary"""
        try:
            if 'Summary' not in wb.sheetnames:
                self.file_handler.log_message("創建基本Summary工作表...")

                # 創建Summary工作表
                summary_ws = wb.create_sheet("Summary")

                # 添加基本標題
                summary_ws.cell(6, 1).value = "Bin"
                summary_ws.cell(6, 2).value = "Count"
                summary_ws.cell(6, 3).value = "%"
                summary_ws.cell(6, 4).value = "Definition"
                summary_ws.cell(6, 5).value = "Note"

                # 添加基本數據（如果有Bin數據的話）
                summary_ws.cell(7, 1).value = "1"
                summary_ws.cell(7, 2).value = "0"
                summary_ws.cell(7, 3).value = "0%"
                summary_ws.cell(7, 4).value = "All Pass"
                summary_ws.cell(7, 5).value = ""

                self.file_handler.log_message("基本Summary工作表創建完成")

        except Exception as e:
            self.file_handler.log_message(f"創建基本Summary工作表失敗: {e}")

def get_summary_csv_converter():
    """獲取Summary CSV轉換器實例"""
    return SummaryCSVConverter()


class Step5Checker(BaseConverter):
    """步驟5檢查器 - 專門檢查CTA→TMT格式轉換結果

    功能：
    - 僅執行步驟1-5，檢查CTA8280格式轉換是否正確
    - 輸出轉換後的Excel檔案供檢查
    - 不執行步驟6-7的數據填充和Summary生成
    """

    def __init__(self):
        super().__init__()

    def check_conversion(self, input_file, output_dir=None):
        """檢查CTA→TMT格式轉換

        Args:
            input_file: 輸入檔案路徑
            output_dir: 輸出目錄

        Returns:
            tuple: (success, output_file_path)
        """
        try:
            self.file_handler.log_message(f"開始步驟5檢查: {input_file}")

            # 步驟1: 檔案讀取與預處理
            self.file_handler.log_message("🚀 步驟1: 檔案讀取與預處理...")
            self.file_handler.log_message("  - 1.1 檔案存在性檢查")
            self.file_handler.log_message("  - 1.2 檔案類型判斷")
            self.file_handler.log_message("  - 1.3 工作目錄準備")
            self.file_handler.log_message("  - 1.4 輸出路徑設置")

            if isinstance(input_file, dict):
                # 如果是字典格式（從GUI傳來），提取實際路徑
                actual_file = input_file.get('processed_path') or input_file.get('original_path')
            else:
                actual_file = input_file

            df, working_file = self.file_handler.read_csv_file(actual_file)
            if df is None:
                self.file_handler.log_message("❌ 步驟1失敗：無法讀取檔案")
                return False, None

            # 步驟4: 格式檢測與分流
            self.file_handler.log_message("🚀 步驟4: 格式檢測與分流...")
            self.file_handler.log_message("  - 4.1 格式特徵檢測")
            self.file_handler.log_message("  - 4.2 測試器類型判斷")
            self.file_handler.log_message("  - 4.3 處理器選擇")

            if not self.detect_tester_type(df, working_file):
                self.file_handler.log_message("❌ 步驟4失敗：檔案格式檢測失敗")
                return False, None

            # 根據格式類型決定處理方式
            if self.my_tester_type == TesterType.CTA8280:
                # 步驟5: CTA格式轉TMT格式（這是我們要檢查的重點）
                self.file_handler.log_message("🚀 步驟5: 開始CTA8280格式轉TMT格式...")
                self.file_handler.log_message("  - 5.1 找到標題行")
                self.file_handler.log_message("  - 5.2 刪除標題行之前的行")
                self.file_handler.log_message("  - 5.3 處理行列操作")
                self.file_handler.log_message("  - 5.4 設置CTA8280標準標題")
                self.file_handler.log_message("  - 5.5 從sum工作表提取信息")
                self.file_handler.log_message("  - 5.6 生成Serial#和Bin#")
                df_converted = self.cta8280_processor.apply_format(df)

                if df_converted is None:
                    self.file_handler.log_message("CTA8280格式轉換失敗")
                    return False, None

                conversion_type = "CTA8280→TMT"

            elif self.my_tester_type == TesterType.TMT:
                # TMT格式：直接使用原始數據，顯示當前格式狀態
                self.file_handler.log_message("🔍 檢測到TMT格式，顯示當前格式狀態...")
                self.file_handler.log_message("  - 格式已為TMT標準格式")
                self.file_handler.log_message("  - 檢查標題行位置和內容")
                self.file_handler.log_message("  - 檢查數據區域結構")
                df_converted = df
                conversion_type = "TMT格式檢查"

            else:
                self.file_handler.log_message(f"不支援的格式類型: {self.my_tester_type}")
                return False, None

            # 創建Excel檔案以供檢查
            output_file = self._create_step5_output_file(df_converted, actual_file, output_dir, conversion_type)

            if output_file:
                self.file_handler.log_message(f"步驟5檢查完成，輸出檔案: {output_file}")
                return True, output_file
            else:
                return False, None

        except Exception as e:
            self.file_handler.log_message(f"步驟5檢查時出錯: {e}")
            import traceback
            self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")
            return False, None

    def _create_step5_output_file(self, df, input_file, output_dir, conversion_type="格式檢查"):
        """創建步驟5檢查的輸出檔案"""
        try:
            # 生成輸出檔案名
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            if output_dir:
                output_file = os.path.join(output_dir, f"{base_name}_step5_check.xlsx")
            else:
                output_file = f"{base_name}_step5_check.xlsx"

            # 創建Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Step5_CTA_to_TMT_Check"

            # 將DataFrame寫入Excel
            for r_idx, row in enumerate(df.itertuples(index=False), 1):
                for c_idx, value in enumerate(row, 1):
                    ws.cell(row=r_idx, column=c_idx, value=value)

            # 添加檢查說明
            ws.insert_rows(1)
            ws.cell(1, 1, f"步驟5檢查：{conversion_type}")
            ws.cell(1, 2, f"原始檔案: {os.path.basename(input_file)}")

            # 設置格式
            from openpyxl.styles import Font, PatternFill
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            ws.cell(1, 1).font = header_font
            ws.cell(1, 1).fill = header_fill
            ws.cell(1, 2).font = header_font
            ws.cell(1, 2).fill = header_fill

            # 保存檔案
            wb.save(output_file)
            self.file_handler.log_message(f"步驟5檢查檔案已保存: {output_file}")

            return output_file

        except Exception as e:
            self.file_handler.log_message(f"創建步驟5檢查檔案時出錯: {e}")
            return None


def get_step5_checker():
    """獲取步驟5檢查器實例"""
    return Step5Checker()
