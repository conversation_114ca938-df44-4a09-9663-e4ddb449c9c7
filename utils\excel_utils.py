#!/usr/bin/env python3
"""
Excel處理工具模組
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font
from openpyxl import Workbook
from config.settings import CTA8280_HEADERS, FILL_EMPTY_SETTINGS

class ExcelHandler:
    """Excel處理類別"""

    def __init__(self):
        # 優化11: 添加file_handler引用以支持日誌記錄
        self.file_handler = None
        try:
            from utils.file_utils import get_file_handler
            self.file_handler = get_file_handler()
        except ImportError:
            pass  # 如果無法導入，保持None
    
    def write_dataframe_to_worksheet(self, df, ws, clean_numeric_format=False):
        """將DataFrame快速寫入工作表（優化版）"""
        import time
        start_time = time.time()

        # 優化1: 預處理數據，避免在寫入時重複處理
        processed_data = self._preprocess_dataframe_for_excel(df, clean_numeric_format)

        # 優化2: 使用批量寫入而不是逐個cell操作
        self._batch_write_to_worksheet(processed_data, ws)

        # 記錄優化效果
        end_time = time.time()
        if hasattr(self, 'file_handler') and self.file_handler:
            self.file_handler.log_message(f"⚡ 優化寫入完成: {len(processed_data)}行 × {len(processed_data[0]) if processed_data else 0}列，耗時{(end_time-start_time)*1000:.1f}ms")

    def _preprocess_dataframe_for_excel(self, df, clean_numeric_format=False):
        """預處理DataFrame數據以優化Excel寫入"""
        values = df.values.tolist()
        processed_data = []

        for r_idx, row in enumerate(values, 1):
            processed_row = []
            for c_idx, value in enumerate(row, 1):
                processed_value = self._process_single_value(value, r_idx, c_idx, clean_numeric_format)
                processed_row.append(processed_value)
            processed_data.append(processed_row)

        return processed_data

    def _process_single_value(self, value, r_idx, c_idx, clean_numeric_format):
        """處理單個值的格式轉換"""
        if pd.notna(value) and str(value).strip():
            str_value = str(value).strip()

            # SPD檔案資料判斷邏輯調整
            if str_value == '0.0000':
                if r_idx >= 13 and c_idx >= 3:
                    return self._convert_to_numeric(value)
                else:
                    return None  # 跳過的值
            else:
                # 嘗試轉換為數字格式
                if self._is_numeric_value(str_value):
                    return self._convert_to_numeric(str_value)
                else:
                    return str_value
        elif pd.notna(value):
            # 處理純數字0的情況
            try:
                if float(value) == 0.0 and r_idx >= 13 and c_idx >= 3:
                    return self._convert_to_numeric(value)
            except (ValueError, TypeError):
                pass

        return None

    def _batch_write_to_worksheet(self, processed_data, ws):
        """超級優化的批量寫入數據到工作表"""
        # 超級優化3: 使用更高效的批量寫入策略
        try:
            # 策略1: 嘗試使用append方法（對於連續數據更快）
            if self._can_use_append_method(processed_data):
                self._write_using_append_method(processed_data, ws)
            else:
                # 策略2: 使用優化的cell設置方法
                self._write_using_optimized_cell_method(processed_data, ws)

        except Exception as e:
            # 降級到原始方法
            if hasattr(self, 'file_handler') and self.file_handler:
                self.file_handler.log_message(f"批量寫入降級到原始方法: {e}")
            self._write_using_original_method(processed_data, ws)

    def _can_use_append_method(self, processed_data):
        """檢查是否可以使用append方法"""
        # 如果數據是連續的且從第一行開始，可以使用append
        if not processed_data:
            return False

        # 檢查是否有太多None值（如果有，append方法效率會降低）
        total_cells = len(processed_data) * len(processed_data[0]) if processed_data else 0
        none_cells = sum(1 for row in processed_data for cell in row if cell is None)

        # 如果None值超過50%，不使用append方法
        return (none_cells / total_cells) < 0.5 if total_cells > 0 else False

    def _write_using_append_method(self, processed_data, ws):
        """使用append方法寫入（適合連續數據）"""
        for row_data in processed_data:
            # 將None替換為空字符串以便append
            clean_row = ['' if cell is None else cell for cell in row_data]
            ws.append(clean_row)

    def _write_using_optimized_cell_method(self, processed_data, ws):
        """使用優化的cell方法寫入"""
        # 批量處理，減少函數調用開銷
        batch_size = 100
        cell_updates = []

        for r_idx, row in enumerate(processed_data, 1):
            for c_idx, value in enumerate(row, 1):
                if value is not None:
                    cell_updates.append((r_idx, c_idx, value))

                    # 達到批量大小時執行批量更新
                    if len(cell_updates) >= batch_size:
                        self._execute_batch_cell_updates(ws, cell_updates)
                        cell_updates = []

        # 處理剩餘的更新
        if cell_updates:
            self._execute_batch_cell_updates(ws, cell_updates)

    def _execute_batch_cell_updates(self, ws, cell_updates):
        """執行批量cell更新"""
        for r, c, val in cell_updates:
            ws.cell(r, c).value = val

    def _write_using_original_method(self, processed_data, ws):
        """原始寫入方法（降級使用）"""
        for r_idx, row in enumerate(processed_data, 1):
            for c_idx, value in enumerate(row, 1):
                if value is not None:
                    ws.cell(r_idx, c_idx).value = value

    def _is_numeric_value(self, value):
        """檢查值是否為數字"""
        if value is None:
            return False

        try:
            float(value)
            return True
        except (ValueError, TypeError):
            return False

    def _convert_to_numeric(self, value):
        """將值轉換為適當的數字格式（整數或浮點數）"""
        if value is None:
            return value

        try:
            # 先轉換為浮點數
            float_value = float(value)

            # 如果是整數（沒有小數部分），返回整數
            if float_value == int(float_value):
                return int(float_value)
            else:
                # 如果是小數，返回浮點數
                return float_value

        except (ValueError, TypeError):
            # 如果轉換失敗，返回原始值
            return value

    def _clean_numeric_value(self, value):
        """清理數值格式：如果是整數就移除小數點，清除空白字符"""
        if not value or not isinstance(value, str):
            return value

        # 清除前後空白字符
        cleaned = value.strip()

        # 如果不是數字，直接返回
        if not cleaned:
            return cleaned

        try:
            # 嘗試轉換為浮點數
            numeric_value = float(cleaned)

            # 如果是整數（沒有小數部分），返回整數格式
            if numeric_value == int(numeric_value):
                return str(int(numeric_value))
            else:
                # 如果是小數，保持原格式但清除空白
                return cleaned

        except (ValueError, TypeError):
            # 如果不是數字，返回清理後的字符串
            return cleaned
    
    def create_workbook_with_sheets(self, sum_data, data11_data, qa_data=None):
        """創建包含多個工作表的工作簿（優化版）"""
        import time
        start_time = time.time()

        # 優化4: 創建工作簿時優化設置
        wb = Workbook()

        # 優化5: 先刪除默認工作表，避免後續刪除操作
        if 'Sheet' in wb.sheetnames:
            wb.remove(wb['Sheet'])

        # 創建sum工作表
        ws_sum = wb.create_sheet("sum")
        self.write_dataframe_to_worksheet(sum_data, ws_sum)

        # 創建Data11工作表
        ws_data11 = wb.create_sheet("Data11")
        self.write_dataframe_to_worksheet(data11_data, ws_data11, clean_numeric_format=True)

        # 創建QAData工作表（如果有數據）
        if qa_data is not None:
            ws_qa = wb.create_sheet("QAData")
            self.write_dataframe_to_worksheet(qa_data, ws_qa)

        # 記錄優化效果
        end_time = time.time()
        if hasattr(self, 'file_handler') and self.file_handler:
            self.file_handler.log_message(f"⚡ 優化工作簿創建完成: {len(wb.sheetnames)}個工作表，耗時{(end_time-start_time)*1000:.1f}ms")

        return wb, ws_sum, ws_data11

    def save_workbook_optimized(self, wb, output_file):
        """超級優化的工作簿保存方法"""
        import time
        start_time = time.time()

        try:
            # 超級優化2: 保存前的多重優化設置
            self._prepare_workbook_for_fast_save(wb)

            # 保存工作簿
            wb.save(output_file)

            # 記錄優化效果
            end_time = time.time()
            if hasattr(self, 'file_handler') and self.file_handler:
                self.file_handler.log_message(f"⚡ 超級優化保存完成: {output_file}，耗時{(end_time-start_time)*1000:.1f}ms")

            return True

        except Exception as e:
            if hasattr(self, 'file_handler') and self.file_handler:
                self.file_handler.log_message(f"❌ 保存失敗: {e}")
            return False

    def _prepare_workbook_for_fast_save(self, wb):
        """準備工作簿以進行快速保存"""
        try:
            # 超級優化策略1: 禁用自動計算
            wb.calculation.calcMode = 'manual'

            # 超級優化策略2: 設置工作簿屬性以加快保存
            if hasattr(wb, 'properties'):
                wb.properties.creator = "OptimizedConverter"
                wb.properties.lastModifiedBy = "OptimizedConverter"

            # 超級優化策略3: 優化工作表設置
            for ws in wb.worksheets:
                # 禁用工作表的自動計算
                if hasattr(ws, 'sheet_properties'):
                    ws.sheet_properties.filterMode = False

                # 優化視圖設置
                if hasattr(ws, 'sheet_view'):
                    ws.sheet_view.showGridLines = True
                    ws.sheet_view.showRowColHeaders = True

            if hasattr(self, 'file_handler') and self.file_handler:
                self.file_handler.log_message("⚡ 工作簿保存前優化設置完成")

        except Exception as e:
            if hasattr(self, 'file_handler') and self.file_handler:
                self.file_handler.log_message(f"工作簿保存前優化設置警告: {e}")
    
    def apply_cta8280_headers(self, ws):
        """應用CTA8280標準標題"""
        for cell_ref, value in CTA8280_HEADERS.items():
            ws[cell_ref] = value
    
    def set_red_font(self, cell):
        """設置紅色字體"""
        cell.font = Font(name="新細明體", size=12, color=FILL_EMPTY_SETTINGS['FONT_COLOR_RED'])
    
    def apply_font_formatting(self, wb):
        """應用字體格式（模擬VBA的字體設定）"""
        default_font = Font(name="新細明體", size=12)

        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            for row in ws.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        # 保留現有的字體顏色，只更新字體名稱和大小
                        current_color = None
                        if cell.font and cell.font.color:
                            current_color = cell.font.color

                        if current_color:
                            # 保留顏色，更新字體名稱和大小
                            cell.font = Font(name="新細明體", size=12, color=current_color)
                        else:
                            # 使用默認字體
                            cell.font = default_font
    
    def worksheet_to_csv(self, ws, csv_file):
        """直接將工作表轉換為CSV"""
        # 轉換為CSV（保留所有行，包括空行）
        csv_data = []
        for row in ws.iter_rows(values_only=True):
            csv_data.append(row)
        
        if csv_data:
            max_cols = max(len(row) for row in csv_data)
            normalized_data = []
            for row in csv_data:
                normalized_row = list(row) + [None] * (max_cols - len(row))
                normalized_data.append(normalized_row)
            
            result_df = pd.DataFrame(normalized_data)
            result_df.to_csv(csv_file, index=False, header=False, encoding='utf-8', na_rep='')
    
    def find_data_markers(self, df):
        """找到數據標記的位置"""
        data_row = None
        qa_data_row = None
        
        for i, val in enumerate(df.iloc[:, 0]):
            val_str = str(val) if pd.notna(val) else ""
            if val_str == '[Data]':
                data_row = i
            elif val_str == '[QAData]':
                qa_data_row = i
        
        return data_row, qa_data_row
    
    def extract_sum_info(self, ws_sum, target_ws):
        """從sum工作表提取信息到目標工作表"""
        info_mapping = {
            'ProgramName': ('B2', lambda x: str(x).split('\\')[-1].replace('.dll', '')),
            'Operator': ('B4', lambda x: x),
            'EndTime': ('B6', lambda x: x),
            'LotID': ('B3', lambda x: x if x and str(x).strip() and str(x) != 'nan' else None),
            'Computer': ('B5', lambda x: x if x and str(x).strip() and str(x) != 'nan' else None)
        }
        
        for r in range(1, min(100, ws_sum.max_row + 1)):
            cell_val = ws_sum.cell(r, 1).value
            if cell_val:
                cell_str = str(cell_val)
                for key, (target_cell, processor) in info_mapping.items():
                    if key in cell_str:
                        if key == 'Computer' and cell_str != 'Computer':
                            continue  # 精確匹配Computer
                        
                        source_val = ws_sum.cell(r, 2).value
                        if source_val:
                            processed_val = processor(source_val)
                            if processed_val:
                                target_ws[target_cell] = processed_val

def get_excel_handler():
    """獲取Excel處理器實例"""
    return ExcelHandler()
