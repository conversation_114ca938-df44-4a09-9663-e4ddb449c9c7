#!/usr/bin/env python3
"""
檢查特定檔案的 item_positions 數值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import openpyxl

def check_item_positions():
    """檢查 G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv 的 item_positions"""

    # 直接檢查已轉換的Excel檔案
    filename = "G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx"

    print(f"=== 檢查檔案: {filename} ===")

    try:
        # 讀取Excel檔案
        wb = openpyxl.load_workbook(filename)
        ws = wb['Data11']

        print(f"工作表最大行數: {ws.max_row}")
        print(f"工作表最大列數: {ws.max_column}")

        # 手動實現項目位置檢測邏輯
        print("\n🔄 分析測試項目位置...")

        item_positions = []
        item_names = {}

        # 檢查從第3列開始的所有列，尋找有效的測試項目
        for col in range(3, ws.max_column + 1):
            # 檢查第8行是否有項目名稱
            item_name = ws.cell(8, col).value

            # 檢查第6行是否有項目編號
            item_number = ws.cell(6, col).value

            # 檢查是否有測試數據（第13行開始）
            has_test_data = False
            for row in range(13, min(20, ws.max_row + 1)):
                test_value = ws.cell(row, col).value
                if test_value is not None and str(test_value).strip():
                    has_test_data = True
                    break

            # 如果有項目名稱或項目編號或測試數據，則認為是有效的測試項目
            if (item_name is not None and str(item_name).strip()) or \
               (item_number is not None) or \
               has_test_data:
                item_positions.append(col)
                if item_name is not None and str(item_name).strip():
                    item_names[col] = str(item_name).strip()
                else:
                    item_names[col] = f"Item_{col}"

        print(f"\n=== 測試項目數據分析結果 ===")
        print(f"項目數量 (item_count): {len(item_positions)}")
        print(f"項目位置 (item_positions): {item_positions}")

        # 顯示詳細的項目信息
        print(f"\n=== 詳細項目信息 ===")
        for i, pos in enumerate(item_positions):
            item_name = item_names.get(pos, f"Item_{pos}")
            item_number = ws.cell(6, pos).value
            print(f"項目 {i+1}: 列位置 {pos}, 名稱: {item_name}, 編號: {item_number}")

        # 檢查第8行的項目名稱
        print(f"\n=== 第8行項目名稱檢查 ===")
        for col in range(3, min(30, ws.max_column + 1)):
            cell_value = ws.cell(8, col).value
            if cell_value is not None and str(cell_value).strip():
                print(f"第8行第{col}列: {cell_value}")

        # 檢查第6行的項目編號
        print(f"\n=== 第6行項目編號檢查 ===")
        for col in range(3, min(30, ws.max_column + 1)):
            cell_value = ws.cell(6, col).value
            if cell_value is not None:
                print(f"第6行第{col}列: {cell_value}")

        # 檢查第10行和第11行的MAX/MIN值
        print(f"\n=== 第10行(MAX)和第11行(MIN)檢查 ===")
        for col in range(3, min(30, ws.max_column + 1)):
            max_value = ws.cell(10, col).value
            min_value = ws.cell(11, col).value
            if max_value is not None or min_value is not None:
                print(f"第{col}列: MAX={max_value}, MIN={min_value}")

        # 重新分析：包含第6行和MAX/MIN填充none的列
        print(f"\n=== 重新分析（包含第6行和MAX/MIN none列）===")

        extended_item_positions = []

        for col in range(3, ws.max_column + 1):
            # 檢查第8行項目名稱
            item_name = ws.cell(8, col).value
            has_item_name = item_name is not None and str(item_name).strip()

            # 檢查第6行項目編號
            item_number = ws.cell(6, col).value
            has_item_number = item_number is not None

            # 檢查第10行和第11行的MAX/MIN值（包括none）
            max_value = ws.cell(10, col).value
            min_value = ws.cell(11, col).value
            has_max_min = (max_value is not None) or (min_value is not None)

            # 檢查是否有測試數據
            has_test_data = False
            for row in range(13, min(20, ws.max_row + 1)):
                test_value = ws.cell(row, col).value
                if test_value is not None and str(test_value).strip():
                    has_test_data = True
                    break

            # 如果有任何一個條件滿足，就認為是測試項目列
            if has_item_name or has_item_number or has_max_min or has_test_data:
                extended_item_positions.append(col)
                print(f"列{col}: 項目名稱={has_item_name}, 項目編號={has_item_number}, MAX/MIN={has_max_min}, 測試數據={has_test_data}")

        print(f"\n=== 擴展後的item_positions ===")
        print(f"擴展項目數量: {len(extended_item_positions)}")
        print(f"擴展項目位置: {extended_item_positions}")

        # 測試新的程式邏輯
        print(f"\n=== 測試新程式邏輯 ===")
        from core.processors.base_processor import BaseProcessor

        # 創建一個臨時的BaseProcessor實例來測試
        class TestProcessor(BaseProcessor):
            def __init__(self):
                pass

            def log_message(self, message):
                print(f"LOG: {message}")

        processor = TestProcessor()
        processor.file_handler = processor  # 簡單的mock

        # 創建測試數據結構
        test_data_summary = {
            'item_names': {},
            'item_positions': [],
            'item_count': 0
        }

        # 調用修改後的方法
        processor._collect_item_data(ws, test_data_summary)

        print(f"新程式識別的項目數量: {test_data_summary['item_count']}")
        print(f"新程式識別的項目位置: {test_data_summary['item_positions']}")

        # 比較結果
        manual_count = len(extended_item_positions)
        program_count = test_data_summary['item_count']

        print(f"\n=== 比較結果 ===")
        print(f"手動分析項目數量: {manual_count}")
        print(f"程式識別項目數量: {program_count}")
        print(f"差異: {program_count - manual_count}")

        if set(extended_item_positions) == set(test_data_summary['item_positions']):
            print("✅ 項目位置完全匹配！")
        else:
            missing_in_program = set(extended_item_positions) - set(test_data_summary['item_positions'])
            extra_in_program = set(test_data_summary['item_positions']) - set(extended_item_positions)
            print(f"❌ 項目位置不匹配")
            if missing_in_program:
                print(f"程式遺漏的列: {sorted(missing_in_program)}")
            if extra_in_program:
                print(f"程式多識別的列: {sorted(extra_in_program)}")

    except Exception as e:
        print(f"❌ 處理過程中出錯: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_item_positions()
