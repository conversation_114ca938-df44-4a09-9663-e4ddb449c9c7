#!/usr/bin/env python3
"""
基礎處理器模組
包含所有處理器的共用功能
"""

import time
from openpyxl.styles import Font, Color
from utils.file_utils import get_file_handler


class BaseProcessor:
    """基礎處理器類 - 包含所有處理器的共用功能"""

    def __init__(self):
        """初始化基礎處理器"""
        self.file_handler = get_file_handler()

    def _find_column_by_keywords(self, ws, row, keywords, max_col=20):
        """統一的列搜索方法（優化版）

        Args:
            ws: 工作表
            row: 搜索的行號
            keywords: 關鍵字列表
            max_col: 最大搜索列數

        Returns:
            int: 找到的列號，未找到返回None
        """
        # 優化：限制搜索範圍，提高效率
        search_limit = min(ws.max_column + 1, max_col)

        for col in range(1, search_limit):
            cell_value = ws.cell(row, col).value
            if cell_value:
                cell_str = str(cell_value).upper()  # 統一轉大寫比較
                if any(keyword.upper() in cell_str for keyword in keywords):
                    return col
        return None

    def _find_column_by_keywords_fast(self, ws, row, keywords, max_col=20):
        """快速列搜索方法（批量優化版）

        Args:
            ws: 工作表
            row: 搜索的行號
            keywords: 關鍵字列表
            max_col: 最大搜索列數

        Returns:
            int: 找到的列號，未找到返回None
        """
        # 批量讀取單元格值，減少單個訪問
        search_limit = min(ws.max_column + 1, max_col)

        # 批量檢查，每次檢查5個單元格
        batch_size = 5
        for col_start in range(1, search_limit, batch_size):
            col_end = min(col_start + batch_size, search_limit)

            for col in range(col_start, col_end):
                cell_value = ws.cell(row, col).value
                if cell_value:
                    cell_str = str(cell_value).upper()
                    if any(keyword.upper() in cell_str for keyword in keywords):
                        return col
        return None

    def _iterate_device_rows_with_data(self, ws, start_row=13, check_data_cols=None):
        """遍歷有數據的設備行

        Args:
            ws: 工作表
            start_row: 開始行號
            check_data_cols: 檢查數據的列範圍，默認為3-20列

        Yields:
            int: 有數據的行號
        """
        if check_data_cols is None:
            check_data_cols = range(3, min(ws.max_column + 1, 20))

        for row_num in range(start_row, ws.max_row + 1):
            # 檢查是否有實際數據
            has_data = any(
                ws.cell(row_num, col).value is not None and str(ws.cell(row_num, col).value).strip()
                for col in check_data_cols
            )
            if has_data:
                yield row_num

    def _is_numeric(self, value, allow_tilde=False):
        """統一的數字檢查方法（整合版）

        Args:
            value: 要檢查的值
            allow_tilde: 是否允許波浪號和點分隔格式

        Returns:
            bool: 是否為有效的數字格式
        """
        if not value:
            return False

        str_value = str(value).strip()

        # 檢查純數字
        try:
            float(str_value)
            return True
        except (ValueError, TypeError):
            pass

        # 如果允許擴展格式
        if allow_tilde:
            # 檢查是否包含波浪號
            if "~" in str_value:
                return True

            # 檢查是否為點分隔的數字格式
            if "." in str_value:
                parts = str_value.split(".")
                try:
                    for part in parts:
                        if part:
                            float(part)
                    return True
                except ValueError:
                    pass

        return False

    def _is_numeric_or_tilde(self, value):
        """數字或波浪號檢查方法（統一版）

        這是 _is_numeric(allow_tilde=True) 的便捷方法
        """
        return self._is_numeric(value, allow_tilde=True)

    def _iterate_device_rows_with_data(self, ws, start_row=13, max_rows=None):
        """統一的設備行遍歷方法

        Args:
            ws: 工作表
            start_row: 開始行號（預設13）
            max_rows: 最大行數限制

        Yields:
            int: 有數據的設備行號
        """
        if max_rows is None:
            max_rows = ws.max_row
        else:
            max_rows = min(ws.max_row, max_rows)

        for row_num in range(start_row, max_rows + 1):
            # 檢查第一列是否有數據（設備編號）
            if ws.cell(row_num, 1).value is not None:
                yield row_num

    def _get_total_device_number(self, ws, max_rows=None):
        """統一的設備數量計算方法

        Args:
            ws: 工作表
            max_rows: 最大行數限制

        Returns:
            int: 設備總數
        """
        device_count = 0
        for _ in self._iterate_device_rows_with_data(ws, max_rows=max_rows):
            device_count += 1
        return device_count

    def _format_percentage(self, value):
        """統一的百分比格式化方法

        Args:
            value: 數值

        Returns:
            str: 格式化的百分比字符串（用於顯示）
        """
        if value == 0:
            return "0%"
        else:
            return f"{value:.2f}%"

    def _get_percentage_value(self, value):
        """獲取百分比的數值（用於Excel數字格式）

        Args:
            value: 百分比數值（如 25.5 表示 25.5%）

        Returns:
            float: 百分比的小數值（如 0.255）
        """
        return value / 100.0

    def _format_item_number(self, cur_item_n, sub_item_n):
        """統一的測試項目編號格式化方法

        Args:
            cur_item_n: 當前項目編號
            sub_item_n: 子項目編號

        Returns:
            str: 格式化的項目編號
        """
        from config.settings import FILL_EMPTY_SETTINGS
        return FILL_EMPTY_SETTINGS['ITEM_NUMBER_FORMAT'].format(cur_item_n, sub_item_n)

    def collect_unified_data_summary(self, ws):
        """統一數據收集方法 - 一次性收集所有基礎數據

        這個方法在步驟6執行，收集所有後續步驟需要的基礎數據，
        避免重複掃描工作表，提高效率和準確性。

        Args:
            ws: 工作表

        Returns:
            dict: 統一的數據摘要結構
        """
        self.file_handler.log_message("🔍 開始統一數據收集...")
        start_time = time.time()

        data_summary = {
            # 基礎計數
            'item_count': 0,
            'device_count': 0,

            # 測試項目相關
            'item_names': {},  # {col_index: item_name}
            'item_positions': [],  # [col_index, ...]

            # Site相關
            'site_info': {
                'site_column': None,
                'site_header': None,
                'total_site_no': 0,
                'site_counts': {},  # {site_num: device_count}
                'site_data': {},    # {device_row: site_num}
                'good_site_n': False,
                'found': False
            },

            # 設備相關
            'device_rows': [],  # [row_index, ...]
            'device_bins': {},  # {row_index: bin_value}

            # 限制值
            'max_limits': {},  # {col_index: max_value}
            'min_limits': {}   # {col_index: min_value}
        }

        # 6.1.1 收集測試項目數據（項目數量、名稱、位置）
        self.file_handler.log_message("🔄 6.1.1 收集測試項目數據...")
        self._collect_item_data(ws, data_summary)

        # 6.1.2 收集設備數據（設備數量、行位置、Bin值）
        self.file_handler.log_message("🔄 6.1.2 收集設備數據...")
        self._collect_device_data(ws, data_summary)

        # 6.1.3 收集Site信息（Site列位置、Site數據、統計）
        self.file_handler.log_message("🔄 6.1.3 收集Site信息...")
        self._collect_site_data(ws, data_summary)

        # 6.1.4 收集限制值（Max/Min值）
        self.file_handler.log_message("🔄 6.1.4 收集限制值...")
        self._collect_limit_values(ws, data_summary)

        # 6.1.5 設置第6行項目編號
        self.file_handler.log_message("🔄 6.1.5 設置第6行項目編號...")
        self._set_item_numbers_unified(ws, data_summary)

        self._log_timing("統一數據收集", start_time)
        self.file_handler.log_message(f"✅ 統一數據收集完成: 項目{data_summary['item_count']}個, 設備{data_summary['device_count']}個, Site{data_summary['site_info']['total_site_no']}個")

        return data_summary

    def _collect_item_data(self, ws, data_summary):
        """收集測試項目數據"""
        # 正確計算項目數量：從第3列開始，找到有數據的列
        item_count = 0
        max_search_col = min(ws.max_column + 1, 700)  # 擴大搜索範圍以包含所有測試項目

        # 收集項目名稱和位置，同時計算實際項目數量
        for col_index in range(3, max_search_col):
            # 檢查是否為測試項目列的多重條件：
            # 1. 第8行、第12行或第7行有項目名稱
            item_name = ws.cell(8, col_index).value or ws.cell(12, col_index).value or ws.cell(7, col_index).value
            has_item_name = item_name and str(item_name).strip()

            # 2. 第6行有項目編號
            item_number = ws.cell(6, col_index).value
            has_item_number = item_number is not None

            # 3. 第10行或第11行有MAX/MIN值（包括"none"）
            max_value = ws.cell(10, col_index).value
            min_value = ws.cell(11, col_index).value
            has_max_min = (max_value is not None) or (min_value is not None)

            # 4. 檢查是否有測試數據（第13行開始的幾行）
            has_test_data = False
            for row in range(13, min(20, ws.max_row + 1)):
                test_value = ws.cell(row, col_index).value
                if test_value is not None and str(test_value).strip():
                    has_test_data = True
                    break

            # 如果滿足任何一個條件，就認為是測試項目列
            if has_item_name or has_item_number or has_max_min or has_test_data:
                # 優先使用項目名稱，如果沒有則使用列索引作為標識
                if has_item_name:
                    data_summary['item_names'][col_index] = str(item_name)
                else:
                    # 為沒有項目名稱但有其他數據的列生成標識
                    data_summary['item_names'][col_index] = f"Item_Col_{col_index}"

                data_summary['item_positions'].append(col_index)
                item_count += 1
            elif item_count > 0:
                # 如果已經找到了一些項目，但當前列沒有任何相關數據，
                # 檢查接下來的幾列是否還有數據，如果連續多列都沒有數據則停止
                empty_count = 0
                for check_col in range(col_index, min(col_index + 10, max_search_col)):
                    check_name = ws.cell(8, check_col).value or ws.cell(12, check_col).value or ws.cell(7, check_col).value
                    check_number = ws.cell(6, check_col).value
                    check_max = ws.cell(10, check_col).value
                    check_min = ws.cell(11, check_col).value
                    if not (check_name or check_number or check_max or check_min):
                        empty_count += 1
                    else:
                        break

                # 如果連續5列都沒有數據，則停止搜索
                if empty_count >= 5:
                    break

        data_summary['item_count'] = item_count
        self.file_handler.log_message(f"收集項目數據: {item_count}個項目，包含第6行項目編號和MAX/MIN值的列")

    def _collect_device_data(self, ws, data_summary):
        """收集設備數據（修正版：檢查C列開始是否有實際數據）"""
        device_rows = []
        device_bins = {}

        # 從第13行開始掃描設備數據
        for row_num in range(13, ws.max_row + 1):
            # 檢查A列（Serial#）是否有值
            if ws.cell(row_num, 1).value is not None:
                # 進一步檢查C列開始是否有實際測試數據
                has_test_data = False
                for col in range(3, min(ws.max_column + 1, 20)):  # 檢查前20列
                    if ws.cell(row_num, col).value is not None:
                        has_test_data = True
                        break

                # 只有當有實際測試數據時才算作有效設備
                if has_test_data:
                    device_rows.append(row_num)
                    # 同時收集Bin值（第2列）
                    bin_value = ws.cell(row_num, 2).value
                    if bin_value:
                        device_bins[row_num] = bin_value
                else:
                    self.file_handler.log_message(f"第{row_num}行：A列有值但C列開始無測試數據，跳過")
            else:
                break  # 遇到空值停止

        data_summary['device_count'] = len(device_rows)
        data_summary['device_rows'] = device_rows
        data_summary['device_bins'] = device_bins

        self.file_handler.log_message(f"收集設備數據: {len(device_rows)}個設備（已排除無測試數據的行）")

    def _collect_site_data(self, ws, data_summary):
        """收集Site數據"""
        site_info = data_summary['site_info']

        # 查找Site列（支持第8行和第12行）
        search_rows = [8, 12]
        for search_row in search_rows:
            for col in range(1, min(20, ws.max_column + 1)):
                header = ws.cell(search_row, col).value
                if header and ('site' in str(header).lower() or 'Site' in str(header)):
                    site_info['site_column'] = col
                    site_info['site_header'] = str(header)
                    site_info['found'] = True
                    self.file_handler.log_message(f"找到Site列: 第{search_row}行第{col}列，標題: {header}")
                    break
            if site_info['found']:
                break

        if not site_info['found']:
            self.file_handler.log_message("未找到Site列")
            return

        # 收集Site數據
        site_counts = {}
        site_data = {}
        max_site_n = 10
        total_site_records = 0  # 統計Site資料筆數

        for device_row in data_summary['device_rows']:
            site_value = ws.cell(device_row, site_info['site_column']).value
            if site_value is not None:
                try:
                    site_num = int(float(str(site_value)))
                    if 1 <= site_num <= max_site_n:
                        site_counts[site_num] = site_counts.get(site_num, 0) + 1
                        site_data[device_row] = site_num
                        total_site_records += 1  # 每個有效Site記錄+1
                except (ValueError, TypeError):
                    pass

        site_info['site_counts'] = site_counts
        site_info['site_data'] = site_data

        # 修復：total_site_no 應該是最大Site號碼，不是Site數量（與舊程式一致）
        if site_counts:
            site_info['total_site_no'] = max(site_counts.keys())
        else:
            site_info['total_site_no'] = 0

        site_info['good_site_n'] = site_info['total_site_no'] > 0
        site_info['total_site_records'] = total_site_records  # 保存Site資料筆數

        self.file_handler.log_message(f"收集Site數據: 最大Site號={site_info['total_site_no']}，實際Site數={len(site_counts)}，{total_site_records}筆Site資料")

    def _collect_limit_values(self, ws, data_summary):
        """收集限制值（Max/Min）"""
        max_limits = {}
        min_limits = {}

        for col_index in data_summary['item_positions']:
            # 收集Max值（第10行）
            max_value = ws.cell(10, col_index).value
            if max_value is not None:
                max_limits[col_index] = max_value

            # 收集Min值（第11行）
            min_value = ws.cell(11, col_index).value
            if min_value is not None:
                min_limits[col_index] = min_value

        data_summary['max_limits'] = max_limits
        data_summary['min_limits'] = min_limits

        self.file_handler.log_message(f"收集限制值: Max {len(max_limits)}個, Min {len(min_limits)}個")

    def _set_item_numbers_unified(self, ws, data_summary):
        """設置第6行的項目編號（統一版本，對應原始程式的 _set_item_numbers 方法）"""
        try:
            item_count = data_summary['item_count']
            if item_count == 0:
                return

            # VBA邏輯：.Cells(6, myLoopItem).Value = myLoopItem + (myMaxPassBinN - 2) + myTotalItemNumber
            # 簡化實現：從第3列開始，依序給值
            my_max_pass_bin_n = 1  # 通常Pass Bin是1
            my_total_item_number = 0  # 簡化為0

            for i, col_index in enumerate(data_summary['item_positions']):
                # 計算Bin編號：列索引 + (Pass Bin - 2) + 總項目數
                bin_number = col_index + (my_max_pass_bin_n - 2) + my_total_item_number
                ws.cell(6, col_index).value = bin_number

            self.file_handler.log_message(f"設置第6行項目編號: 從第3列開始，共{item_count}個項目")

        except Exception as e:
            self.file_handler.log_message(f"設置第6行項目編號時出錯: {e}")

    def _get_column_letter(self, col_num):
        """將列號轉換為列字母

        Args:
            col_num: 列號（1-based）

        Returns:
            str: 列字母（如A, B, C...）
        """
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def _log_error(self, process_name, error):
        """記錄錯誤日誌"""
        self.file_handler.log_message(f"{process_name}時出錯: {error}")
        import traceback
        self.file_handler.log_message(f"錯誤詳情: {traceback.format_exc()}")

    def _log_timing(self, process_name, start_time, end_time=None):
        """記錄執行時間日誌

        Args:
            process_name: 處理名稱
            start_time: 開始時間
            end_time: 結束時間，如果為None則使用當前時間
        """
        if end_time is None:
            end_time = time.time()
        duration = end_time - start_time
        if duration < 0.001:
            self.file_handler.log_message(f"⏱️ {process_name} 執行時間: <1ms")
        elif duration < 1:
            self.file_handler.log_message(f"⏱️ {process_name} 執行時間: {duration*1000:.1f}ms")
        else:
            self.file_handler.log_message(f"⏱️ {process_name} 執行時間: {duration:.2f}s")
