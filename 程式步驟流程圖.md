# 🚀 TMT檔案轉換程式完整步驟流程圖

## 📋 **總覽：7個主要步驟**

```
步驟1: 檔案讀取與預處理
步驟2: 檔案解壓縮（如需要）
步驟3: SPD轉CSV（如需要）
步驟4: 格式檢測與分流
步驟5: CTA格式轉TMT格式（如需要）
步驟6: 數據填充與統一收集
步驟7: Summary工作表生成
```

---

## 🔍 **詳細步驟說明**

### **步驟1：檔案讀取與預處理**
**位置**：`converters.py` → `convert()` 方法
**功能**：
- 檢查輸入檔案是否存在
- 判斷檔案類型（.spd, .csv, .xlsx等）
- 創建工作目錄
- 設置輸出檔案名稱

**子步驟**：
- 1.1 檔案存在性檢查
- 1.2 檔案類型判斷
- 1.3 工作目錄準備
- 1.4 輸出路徑設置

---

### **步驟2：檔案解壓縮（條件性執行）**
**位置**：`file_handler.py` → `extract_spd_file()`
**觸發條件**：輸入檔案為 `.spd` 格式
**功能**：
- 解壓縮SPD檔案
- 提取內部的CSV檔案
- 清理臨時檔案

**子步驟**：
- 2.1 SPD檔案解壓縮
- 2.2 內部檔案提取
- 2.3 臨時檔案清理

---

### **步驟3：SPD轉CSV（條件性執行）**
**位置**：`file_handler.py` → `read_csv_file()`
**觸發條件**：檔案為SPD格式或需要格式轉換
**功能**：
- 將SPD內容轉換為標準CSV格式
- 處理編碼問題
- 數據格式標準化

**子步驟**：
- 3.1 編碼檢測與轉換
- 3.2 CSV格式標準化
- 3.3 數據完整性檢查

---

### **步驟4：格式檢測與分流**
**位置**：`converters.py` → `detect_tester_type()`
**功能**：
- 檢測檔案格式類型（TMT、CTA8280、其他）
- 根據格式決定後續處理流程
- 設置相應的處理器

**子步驟**：
- 4.1 格式特徵檢測
- 4.2 測試器類型判斷
- 4.3 處理器選擇

**分流結果**：
- **TMT格式** → 直接進入步驟6
- **CTA8280格式** → 進入步驟5
- **其他格式** → 通用處理進入步驟6

---

### **步驟5：CTA格式轉TMT格式（條件性執行）**
**位置**：`cta8280_processor.py` → `apply_format()`
**觸發條件**：檢測到CTA8280格式
**功能**：將CTA8280格式轉換為標準TMT格式

**子步驟**：
- **5.1 找到標題行**
  - 搜索關鍵字（Serial#, SW_Bin等）
  - 確定數據開始位置
  
- **5.2 刪除標題行之前的行**
  - 移除無用的元數據行
  - 保留有效數據區域
  
- **5.3 處理行列操作**
  - 重新排列數據結構
  - 標準化列順序
  
- **5.4 設置CTA8280標準標題**
  - 設置TMT格式標準標題
  - 建立列映射關係
  
- **5.5 從sum工作表提取信息**
  - 提取測試統計信息
  - 合併相關數據
  
- **5.6 生成Serial#和Bin#**
  - 為每個設備生成序號
  - 設置Bin值

---

### **步驟6：數據填充與統一收集**
**位置**：`fill_empty_processor.py` → `apply_processing()`
**功能**：統一收集所有基礎數據，填充缺失值

**子步驟**：
- **6.1 統一數據收集**
  - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
  - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
  - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
  - 6.1.4 收集限制值（Max/Min值）
  - 6.1.5 設置第6行項目編號
  
- **6.2 填充測試項目名稱和編號**
  - 檢查第7行、第8行是否有項目信息
  - 填充缺失的項目編號和名稱
  - 設置紅色字體標記
  
- **6.3 填充Min/Max值**
  - 檢查第10行（Max值）和第11行（Min值）
  - 填充預設限制值
  - 設置紅色字體標記
  
- **6.4 數據驗證與整理**
  - 驗證數據完整性
  - 準備統一數據摘要

**輸出**：統一的數據摘要結構（data_summary）

---

### **步驟7：Summary工作表生成**
**位置**：`device_bin_processor.py` → `apply_processing(create_summary=True)`
**功能**：使用步驟6的統一數據生成完整的Summary工作表

**子步驟**：
- **7.1 VBA核心分析（統一數據版）**
  - 7.1.1 收集原始Bin值
  - 7.1.2 執行設備分析（測試項目失敗檢測）
  - 7.1.3 計算Bin統計
  - 7.1.4 應用染色邏輯（Excel格式）
  
- **7.2 創建增強Summary工作表**
  - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
  - 7.2.2 添加原始檔案超連結（C1）
  - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
  - 7.2.4 填充F列開始的Site統計（數量和百分比）
  - 7.2.5 填充測試項目Bin數據
  - 7.2.6 設置AutoFilter和排序

**輸出**：完整的Excel檔案，包含Data11工作表和Summary工作表

---

## 🔄 **步驟間的數據流**

```
步驟1-3: 檔案準備
    ↓
步驟4: 格式檢測
    ↓
步驟5: CTA→TMT轉換（條件性）
    ↓
步驟6: 統一數據收集 → data_summary
    ↓
步驟7: Summary生成（使用data_summary）
```

## ⚡ **優化特點**

1. **統一數據收集**：步驟6一次性收集所有數據，避免重複掃描
2. **條件性執行**：根據格式類型決定是否執行步驟2、3、5
3. **數據傳遞**：步驟6的data_summary直接傳遞給步驟7
4. **效率提升**：避免了原本4-5次的重複計算

## 🎯 **關鍵改進**

- **問題1修復**：設備數量計算排除無測試數據的行
- **問題2修復**：Summary工作表原始檔案使用超連結
- **問題3修復**：F列開始正確顯示Site統計（數量+百分比）
- **統一整合**：所有重複計算邏輯已整合到步驟6

---

## 📁 **程式碼檔案對應表**

### **主要控制器**
- `converters.py` - 主要轉換控制器，包含步驟1-4和統一處理管道
- `main.py` - GUI介面，調用轉換器

### **處理器模組**
- `base_processor.py` - 基礎處理器，包含統一數據收集邏輯
- `cta8280_processor.py` - 步驟5：CTA格式轉換處理器
- `fill_empty_processor.py` - 步驟6：數據填充處理器
- `device_bin_processor.py` - 步驟7：Summary生成處理器

### **工具模組**
- `file_handler.py` - 檔案處理工具（步驟1-3）
- `excel_utils.py` - Excel操作工具
- `config/settings.py` - 配置設定

### **步驟執行對應**

| 步驟 | 主要檔案 | 主要方法 | 功能描述 |
|------|----------|----------|----------|
| 步驟1 | `converters.py` | `convert()` | 檔案讀取與預處理 |
| 步驟2 | `file_handler.py` | `extract_spd_file()` | SPD檔案解壓縮 |
| 步驟3 | `file_handler.py` | `read_csv_file()` | SPD轉CSV |
| 步驟4 | `converters.py` | `detect_tester_type()` | 格式檢測與分流 |
| 步驟5 | `cta8280_processor.py` | `apply_format()` | CTA轉TMT格式 |
| 步驟6 | `fill_empty_processor.py` | `apply_processing()` | 數據填充與統一收集 |
| 步驟7 | `device_bin_processor.py` | `apply_processing()` | Summary工作表生成 |

### **統一數據收集子步驟**
| 子步驟 | 檔案 | 方法 | 功能 |
|--------|------|------|------|
| 6.1.1 | `base_processor.py` | `_collect_item_data()` | 收集測試項目數據 |
| 6.1.2 | `base_processor.py` | `_collect_device_data()` | 收集設備數據 |
| 6.1.3 | `base_processor.py` | `_collect_site_data()` | 收集Site信息 |
| 6.1.4 | `base_processor.py` | `_collect_limit_values()` | 收集限制值 |
| 6.1.5 | `base_processor.py` | `_set_item_numbers_unified()` | 設置第6行項目編號 |

---

## 🚀 **執行流程總結**

1. **入口點**：`main.py` GUI或直接調用 `converters.py`
2. **主控制器**：`FullConverter.convert()` 方法
3. **統一管道**：`_unified_processing_pipeline()` 執行步驟4-7
4. **數據傳遞**：步驟6 → data_summary → 步驟7
5. **輸出結果**：完整的Excel檔案（Data11 + Summary工作表）

這個重新整理的步驟流程確保了：
- ✅ 清晰的步驟劃分（1-7步）
- ✅ 明確的檔案職責分工
- ✅ 統一的數據流向
- ✅ 高效的處理邏輯
