#!/usr/bin/env python3
"""
TMT檔案轉換程式主程式
7步驟統一處理架構的入口點

📋 總覽：7個主要步驟
步驟1: 檔案讀取與預處理
步驟2: 檔案解壓縮（如需要）
步驟3: SPD轉CSV（如需要）
步驟4: 格式檢測與分流
步驟5: CTA格式轉TMT格式（如需要）
步驟6: 數據填充與統一收集
步驟7: Summary工作表生成

⚡ 優化特點：
1. 統一數據收集：步驟6一次性收集所有數據，避免重複掃描
2. 條件性執行：根據格式類型決定是否執行步驟2、3、5
3. 數據傳遞：步驟6的data_summary直接傳遞給步驟7
4. 效率提升：避免了原本4-5次的重複計算
"""

import sys
import tkinter as tk
from gui.main_window import CSVConverterGUI

def main():
    """主函數 - 啟動TMT檔案轉換程式GUI介面

    功能：
    - 初始化GUI主視窗
    - 提供7步驟轉換流程的使用者介面
    - 支援完整轉換和快速Summary模式
    """
    try:
        # 創建主視窗
        root = tk.Tk()
        app = CSVConverterGUI(root)

        # 啟動GUI事件循環
        root.mainloop()

    except Exception as e:
        print(f"TMT轉換程式啟動失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
