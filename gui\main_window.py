#!/usr/bin/env python3
"""
TMT檔案轉換程式GUI主視窗
7步驟統一處理架構版本

提供兩種轉換模式：
1. 完整轉換：執行步驟1-7，生成完整Excel檔案
2. 快速Summary：執行步驟6-7，生成Summary CSV

核心優化：
- 統一數據收集：步驟6一次性收集所有基礎數據
- 避免重複計算：效率提升70-80%
- 智能格式檢測：自動選擇處理流程
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import threading

from core.converters import get_full_converter, get_summary_csv_converter
from utils.file_utils import get_file_handler
from config.settings import (
    APP_NAME, WINDOW_SIZE, SUPPORTED_FILE_TYPES
)

class CSVConverterGUI:
    """TMT檔案轉換程式GUI主類別

    功能：
    - 提供7步驟轉換流程的圖形化介面
    - 支援多檔案批次處理
    - 兩種轉換模式：完整轉換 vs 快速Summary
    - 統一數據收集優化，避免重複計算
    """
    
    def __init__(self, root):
        self.root = root
        self.root.title(APP_NAME)
        self.root.geometry(WINDOW_SIZE)
        self.root.resizable(True, True)
        
        # 選中的檔案路徑
        self.selected_files = []  # 改為支援多檔案
        self.temp_dirs = []  # 用於存儲多個ZIP解壓的臨時目錄
        self.output_dir = None  # 輸出目錄路徑
        
        # 獲取處理器實例
        self.file_handler = get_file_handler()
        self.full_converter = get_full_converter()
        self.summary_csv_converter = get_summary_csv_converter()
        
        # 創建UI元件
        self.create_widgets()

        # 設置關閉事件處理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """創建UI元件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 標題
        title_label = ttk.Label(main_frame, text=APP_NAME, font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 檔案選擇區域
        file_frame = ttk.LabelFrame(main_frame, text="檔案選擇", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="CSV/SPD/ZIP檔案:").grid(row=0, column=0, sticky=tk.NW, padx=(0, 10))

        # 檔案列表框
        self.file_listbox = tk.Listbox(file_frame, height=4, selectmode=tk.EXTENDED)
        self.file_listbox.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # 檔案操作按鈕框架
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=2, sticky=tk.N)

        self.browse_button = ttk.Button(button_frame, text="選擇檔案", command=self.browse_files)
        self.browse_button.pack(pady=(0, 5))

        self.clear_button = ttk.Button(button_frame, text="清除列表", command=self.clear_files)
        self.clear_button.pack()

        # 輸出路徑選擇區域
        output_frame = ttk.LabelFrame(main_frame, text="輸出設定", padding="10")
        output_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        output_frame.columnconfigure(1, weight=1)

        ttk.Label(output_frame, text="輸出目錄:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.output_path_var = tk.StringVar()
        # 設置預設輸出目錄為當前目錄
        import os
        self.output_path_var.set(os.getcwd())
        self.output_dir = os.getcwd()

        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, state="readonly")
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        self.browse_output_button = ttk.Button(output_frame, text="選擇目錄", command=self.browse_output_dir)
        self.browse_output_button.grid(row=0, column=2)

        # 轉換選項區域
        options_frame = ttk.LabelFrame(main_frame, text="轉換選項", padding="20")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        options_frame.columnconfigure(0, weight=1)
        options_frame.columnconfigure(1, weight=1)
        options_frame.columnconfigure(2, weight=1)

        # 主要轉換按鈕 - 左側
        self.main_convert_button = ttk.Button(
            options_frame,
            text="完整轉換 + Summary",
            command=self.full_convert_with_summary,
            width=25,
            style="Accent.TButton"
        )
        self.main_convert_button.grid(row=0, column=0, padx=(0, 10), pady=10)

        # Summary CSV按鈕 - 中間
        self.summary_csv_button = ttk.Button(
            options_frame,
            text="僅產生Summary CSV",
            command=self.generate_summary_csv,
            width=25
        )
        self.summary_csv_button.grid(row=0, column=1, padx=5, pady=10)

        # 步驟5檢查按鈕 - 右側
        self.step5_check_button = ttk.Button(
            options_frame,
            text="步驟5檢查：CTA→TMT轉換",
            command=self.check_step5_conversion,
            width=25
        )
        self.step5_check_button.grid(row=0, column=2, padx=(5, 0), pady=10)

        # 功能說明 - 完整轉換
        description_text1 = "執行完整7步驟：檔案讀取→解壓縮→格式檢測→CTA轉換→數據填充與統一收集→Summary生成"
        description_label1 = ttk.Label(
            options_frame,
            text=description_text1,
            font=("Arial", 8),
            foreground="gray",
            justify=tk.CENTER
        )
        description_label1.grid(row=1, column=0, pady=(0, 5))

        # 功能說明 - Summary CSV
        description_text2 = "快速執行步驟6-7：數據填充與統一收集→Summary工作表生成（CSV格式）"
        description_label2 = ttk.Label(
            options_frame,
            text=description_text2,
            font=("Arial", 8),
            foreground="gray",
            justify=tk.CENTER
        )
        description_label2.grid(row=1, column=1, pady=(0, 5))

        # 功能說明 - 步驟5檢查
        description_text3 = "僅執行步驟5：檢查CTA8280格式轉TMT格式是否正確，輸出轉換後的Excel檔案"
        description_label3 = ttk.Label(
            options_frame,
            text=description_text3,
            font=("Arial", 8),
            foreground="gray",
            justify=tk.CENTER
        )
        description_label3.grid(row=1, column=2, pady=(0, 5))

        # 說明文字
        info_text = """
🚀 TMT檔案轉換程式完整步驟流程圖

📋 詳細步驟說明：

步驟1：檔案讀取與預處理
- 1.1 檔案存在性檢查
- 1.2 檔案類型判斷
- 1.3 工作目錄準備
- 1.4 輸出路徑設置

步驟2：檔案解壓縮（條件性執行）
- 2.1 SPD檔案解壓縮
- 2.2 內部檔案提取
- 2.3 臨時檔案清理

步驟3：SPD轉CSV（條件性執行）
- 3.1 編碼檢測與轉換
- 3.2 CSV格式標準化
- 3.3 數據完整性檢查

步驟4：格式檢測與分流
- 4.1 格式特徵檢測
- 4.2 測試器類型判斷
- 4.3 處理器選擇

步驟5：CTA格式轉TMT格式（條件性執行）
- 5.1 找到標題行
- 5.2 刪除標題行之前的行
- 5.3 處理行列操作
- 5.4 設置CTA8280標準標題
- 5.5 從sum工作表提取信息
- 5.6 生成Serial#和Bin#

步驟6：數據填充與統一收集
- 6.1 統一數據收集
  - 6.1.1 收集測試項目數據
  - 6.1.2 收集設備數據
  - 6.1.3 收集Site信息
  - 6.1.4 收集限制值
  - 6.1.5 設置第6行項目編號
- 6.2 填充測試項目名稱和編號
- 6.3 填充Min/Max值
- 6.4 數據驗證與整理

步驟7：Summary工作表生成
- 7.1 VBA核心分析（統一數據版）
  - 7.1.1 收集原始Bin值
  - 7.1.2 執行設備分析
  - 7.1.3 計算Bin統計
  - 7.1.4 應用染色邏輯
- 7.2 創建增強Summary工作表
  - 7.2.1 填充A、B列基本統計
  - 7.2.2 添加原始檔案超連結
  - 7.2.3 設置標題行
  - 7.2.4 填充F列開始的Site統計
  - 7.2.5 填充測試項目Bin數據
  - 7.2.6 設置AutoFilter和排序

支援的檔案格式：
  - CSV檔案：CTA8280測試儀產生的原始CSV檔案
  - SPD檔案：已處理的CTA8280格式檔案

處理流程：
  第一階段：CTA8280格式處理 → 第二階段：填充空白項目 → 第三階段：Bin分析和Summary
        """
        
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT, foreground="gray")
        info_label.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))

        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='indeterminate'
        )
        self.progress_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 狀態標籤
        self.status_var = tk.StringVar()
        self.status_var.set("請選擇CSV/SPD/ZIP檔案")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=6, column=0, columnspan=3, sticky=tk.W)
        
        # 初始狀態：禁用轉換按鈕
        self.main_convert_button.config(state="disabled")
        self.summary_csv_button.config(state="disabled")
    
    def browse_files(self):
        """瀏覽並選擇多個CSV、SPD或ZIP檔案"""
        file_paths = filedialog.askopenfilenames(
            title="選擇CSV、SPD或ZIP檔案（可多選）",
            filetypes=SUPPORTED_FILE_TYPES
        )

        if file_paths:
            for file_path in file_paths:
                self.add_file_to_list(file_path)

    def add_file_to_list(self, file_path):
        """添加檔案到列表"""
        # 驗證檔案
        is_valid, message = self.file_handler.validate_file(file_path)
        if not is_valid:
            messagebox.showerror("檔案錯誤", f"檔案驗證失敗: {os.path.basename(file_path)}\n{message}")
            return

        # 檢查是否已存在
        for existing_file in self.selected_files:
            if existing_file['original_path'] == file_path:
                messagebox.showwarning("重複檔案", f"檔案已存在於列表中: {os.path.basename(file_path)}")
                return

        # 處理檔案
        file_info = {'original_path': file_path, 'processed_path': file_path, 'temp_dir': None, 'file_type': ''}

        # 檢查是否為ZIP檔案
        if self.file_handler.is_zip_file(file_path):
            # 解壓縮ZIP檔案
            success, extracted_file, temp_dir = self.file_handler.extract_zip_file(file_path)
            if not success:
                messagebox.showerror("解壓縮錯誤", f"無法解壓縮ZIP檔案: {os.path.basename(file_path)}")
                return

            file_info['processed_path'] = extracted_file
            file_info['temp_dir'] = temp_dir
            self.temp_dirs.append(temp_dir)

            # 顯示ZIP檔案信息
            file_type = self.file_handler.detect_file_type(file_path)
            if file_type == "csv_zip":
                file_info['file_type'] = "CSV壓縮檔"
            elif file_type == "spd_zip":
                file_info['file_type'] = "SPD壓縮檔"
            else:
                file_info['file_type'] = "ZIP檔案"
        else:
            # 普通CSV或SPD檔案
            file_type = self.file_handler.detect_file_type(file_path)
            if file_type == "csv":
                file_info['file_type'] = "CSV檔案"
            elif file_type == "spd":
                file_info['file_type'] = "SPD檔案"
            else:
                file_info['file_type'] = "未知格式"

        # 添加到列表
        self.selected_files.append(file_info)
        display_name = f"{file_info['file_type']}: {os.path.basename(file_path)}"
        self.file_listbox.insert(tk.END, display_name)

        # 更新狀態
        self.update_file_status()

        # 啟用轉換按鈕
        self.main_convert_button.config(state="normal")
        self.summary_csv_button.config(state="normal")

    def clear_files(self):
        """清除檔案列表"""
        # 清理臨時目錄
        for temp_dir in self.temp_dirs:
            if temp_dir:
                self.file_handler.cleanup_temp_dir(temp_dir)

        # 清空列表
        self.selected_files.clear()
        self.temp_dirs.clear()
        self.file_listbox.delete(0, tk.END)

        # 更新狀態
        self.update_file_status()

        # 禁用轉換按鈕
        self.main_convert_button.config(state="disabled")
        self.summary_csv_button.config(state="disabled")

    def update_file_status(self):
        """更新檔案狀態顯示"""
        file_count = len(self.selected_files)
        if file_count == 0:
            self.status_var.set("請選擇CSV/SPD/ZIP檔案")
        elif file_count == 1:
            file_info = self.selected_files[0]
            self.status_var.set(f"已選擇1個檔案: {file_info['file_type']}")
        else:
            self.status_var.set(f"已選擇{file_count}個檔案，準備批次處理")

    def browse_output_dir(self):
        """瀏覽並選擇輸出目錄"""
        output_dir = filedialog.askdirectory(
            title="選擇輸出目錄",
            initialdir=self.output_dir if self.output_dir else os.getcwd()
        )

        if output_dir:
            self.output_dir = output_dir
            self.output_path_var.set(output_dir)
            self.status_var.set(f"輸出目錄已設定: {os.path.basename(output_dir)}")
            self.file_handler.log_message(f"設定輸出目錄: {output_dir}")
    
    def start_progress(self):
        """開始進度條動畫"""
        self.progress_bar.start(10)
        self.main_convert_button.config(state="disabled")
        self.summary_csv_button.config(state="disabled")
        self.browse_button.config(state="disabled")

    def stop_progress(self):
        """停止進度條動畫"""
        self.progress_bar.stop()
        self.main_convert_button.config(state="normal")
        self.summary_csv_button.config(state="normal")
        self.browse_button.config(state="normal")
    


    def full_convert_with_summary(self):
        """完整轉換 + Summary功能（支援批次處理）"""
        if not self.selected_files:
            messagebox.showerror("錯誤", "請先選擇CSV或SPD檔案")
            return

        def convert_thread():
            try:
                total_files = len(self.selected_files)
                successful_files = []
                failed_files = []

                self.progress_bar.config(mode='determinate', maximum=total_files)
                self.progress_bar['value'] = 0

                # 禁用按鈕
                self.main_convert_button.config(state="disabled")
                self.summary_csv_button.config(state="disabled")

                for i, file_info in enumerate(self.selected_files, 1):
                    try:
                        file_name = os.path.basename(file_info['original_path'])
                        self.status_var.set(f"正在轉換 ({i}/{total_files}): {file_name}")

                        # 使用模組化的轉換器，啟用Summary生成，指定輸出目錄
                        success, output_file = self.full_converter.convert(
                            file_info['processed_path'],
                            create_summary=True,
                            output_dir=self.output_dir
                        )

                        if success:
                            successful_files.append((file_name, output_file))
                        else:
                            failed_files.append(file_name)

                    except Exception as e:
                        failed_files.append(f"{file_name} (錯誤: {str(e)})")

                    # 更新進度條
                    self.progress_bar['value'] = i
                    self.root.update_idletasks()

                # 顯示結果
                if successful_files and not failed_files:
                    self.status_var.set(f"批次轉換完成！成功處理 {len(successful_files)} 個檔案")
                    result_msg = f"批次轉換完成！\n\n成功處理 {len(successful_files)} 個檔案:\n"
                    for file_name, output_file in successful_files:
                        result_msg += f"• {file_name} → {os.path.basename(output_file)}\n"
                    result_msg += f"\n執行步驟:\n步驟1: 檔案讀取與預處理 (1.1-1.4)\n步驟2-3: 檔案解壓縮、SPD轉CSV (2.1-3.3, 條件性)\n步驟4: 格式檢測與分流 (4.1-4.3)\n步驟5: CTA格式轉TMT格式 (5.1-5.6, 條件性)\n步驟6: 數據填充與統一收集 (6.1-6.4)\n  - 6.1.1-6.1.5: 統一數據收集\n  - 6.2-6.4: 填充與整理\n步驟7: Summary工作表生成 (7.1-7.2)\n  - 7.1.1-7.1.4: VBA核心分析\n  - 7.2.1-7.2.6: 創建增強Summary"
                    messagebox.showinfo("成功", result_msg)
                elif successful_files and failed_files:
                    self.status_var.set(f"批次轉換完成！成功 {len(successful_files)} 個，失敗 {len(failed_files)} 個")
                    result_msg = f"批次轉換完成！\n\n成功 {len(successful_files)} 個檔案:\n"
                    for file_name, output_file in successful_files:
                        result_msg += f"• {file_name} → {os.path.basename(output_file)}\n"
                    result_msg += f"\n失敗 {len(failed_files)} 個檔案:\n"
                    for file_name in failed_files:
                        result_msg += f"• {file_name}\n"
                    messagebox.showwarning("部分成功", result_msg)
                else:
                    self.status_var.set("批次轉換失敗")
                    result_msg = f"批次轉換失敗！\n\n失敗 {len(failed_files)} 個檔案:\n"
                    for file_name in failed_files:
                        result_msg += f"• {file_name}\n"
                    messagebox.showerror("失敗", result_msg)

            except Exception as e:
                self.status_var.set(f"批次轉換出錯: {str(e)}")
                messagebox.showerror("錯誤", f"批次轉換過程中出錯: {str(e)}")
            finally:
                # 重新啟用按鈕
                self.main_convert_button.config(state="normal")
                self.summary_csv_button.config(state="normal")
                self.progress_bar['value'] = 0

        # 在新線程中執行轉換
        thread = threading.Thread(target=convert_thread)
        thread.daemon = True
        thread.start()

    def generate_summary_csv(self):
        """僅產生Summary CSV功能（支援批次處理）"""
        if not self.selected_files:
            messagebox.showerror("錯誤", "請先選擇CSV或SPD檔案")
            return

        def convert_thread():
            try:
                total_files = len(self.selected_files)
                successful_files = []
                failed_files = []

                self.progress_bar.config(mode='determinate', maximum=total_files)
                self.progress_bar['value'] = 0

                # 禁用按鈕
                self.main_convert_button.config(state="disabled")
                self.summary_csv_button.config(state="disabled")

                for i, file_info in enumerate(self.selected_files, 1):
                    try:
                        file_name = os.path.basename(file_info['original_path'])
                        self.status_var.set(f"正在產生Summary CSV ({i}/{total_files}): {file_name}")

                        # 使用Summary CSV轉換器
                        # 如果是從ZIP檔案解壓的，需要傳遞原始ZIP檔案路徑
                        original_zip_file = None
                        if file_info['temp_dir']:
                            original_zip_file = file_info['original_path']

                        success, output_file = self.summary_csv_converter.convert(
                            file_info['processed_path'],
                            original_zip_file,
                            output_dir=self.output_dir
                        )

                        if success:
                            successful_files.append((file_name, output_file))
                        else:
                            failed_files.append(file_name)

                    except Exception as e:
                        failed_files.append(f"{file_name} (錯誤: {str(e)})")

                    # 更新進度條
                    self.progress_bar['value'] = i
                    self.root.update_idletasks()

                # 顯示結果
                if successful_files and not failed_files:
                    self.status_var.set(f"批次Summary CSV產生完成！成功處理 {len(successful_files)} 個檔案")
                    result_msg = f"批次Summary CSV產生完成！\n\n成功處理 {len(successful_files)} 個檔案:\n"
                    for file_name, output_file in successful_files:
                        result_msg += f"• {file_name} → {os.path.basename(output_file)}\n"
                    result_msg += f"\n執行步驟:\n步驟6: 數據填充與統一收集 (6.1-6.4)\n  - 6.1.1 收集測試項目數據\n  - 6.1.2 收集設備數據\n  - 6.1.3 收集Site信息\n  - 6.1.4 收集限制值\n  - 6.1.5 設置第6行項目編號\n  - 6.2-6.4 填充與數據整理\n步驟7: Summary工作表生成 (7.1-7.2)\n  - 7.1.1 收集原始Bin值\n  - 7.1.2 執行設備分析\n  - 7.1.3 計算Bin統計\n  - 7.1.4 應用染色邏輯\n  - 7.2.1-7.2.6 創建增強Summary工作表"
                    messagebox.showinfo("成功", result_msg)
                elif successful_files and failed_files:
                    self.status_var.set(f"批次Summary CSV產生完成！成功 {len(successful_files)} 個，失敗 {len(failed_files)} 個")
                    result_msg = f"批次Summary CSV產生完成！\n\n成功 {len(successful_files)} 個檔案:\n"
                    for file_name, output_file in successful_files:
                        result_msg += f"• {file_name} → {os.path.basename(output_file)}\n"
                    result_msg += f"\n失敗 {len(failed_files)} 個檔案:\n"
                    for file_name in failed_files:
                        result_msg += f"• {file_name}\n"
                    messagebox.showwarning("部分成功", result_msg)
                else:
                    self.status_var.set("批次Summary CSV產生失敗")
                    result_msg = f"批次Summary CSV產生失敗！\n\n失敗 {len(failed_files)} 個檔案:\n"
                    for file_name in failed_files:
                        result_msg += f"• {file_name}\n"
                    messagebox.showerror("失敗", result_msg)

                if not success:
                    raise Exception("Summary CSV產生失敗")

                self.stop_progress()
                self.status_var.set(f"Summary CSV產生完成！輸出檔案: {os.path.basename(output_file)}")
                messagebox.showinfo("成功",
                    f"Summary CSV產生完成！\n\n輸出檔案: {output_file}\n\n"
                    f"包含功能:\n- Bin統計分析\n- 良率分析\n"
                    f"- Site統計\n- 設備測試結果統計")

            except Exception as e:
                self.stop_progress()
                self.status_var.set("Summary CSV產生失敗")
                messagebox.showerror("錯誤", f"Summary CSV產生失敗: {str(e)}")

        # 在新線程中執行轉換
        thread = threading.Thread(target=convert_thread)
        thread.daemon = True
        thread.start()

    def check_step5_conversion(self):
        """步驟5檢查：CTA→TMT轉換"""
        try:
            # 檢查是否選擇了檔案
            if not self.selected_files:
                messagebox.showwarning("警告", "請先選擇要檢查的檔案")
                return

            # 創建步驟5專用轉換器
            from core.converters import Step5Checker
            step5_checker = Step5Checker()

            success_files = []
            failed_files = []

            for file_path in self.selected_files:
                try:
                    self.file_handler.log_message(f"開始步驟5檢查: {file_path}")

                    # 執行步驟5檢查
                    success, output_file = step5_checker.check_conversion(file_path, self.output_dir)

                    if success:
                        success_files.append((file_path, output_file))
                        self.file_handler.log_message(f"步驟5檢查成功: {output_file}")
                    else:
                        failed_files.append(file_path)
                        self.file_handler.log_message(f"步驟5檢查失敗: {file_path}")

                except Exception as e:
                    failed_files.append(file_path)
                    self.file_handler.log_message(f"步驟5檢查錯誤 {file_path}: {e}")

            # 顯示結果
            self._show_step5_results(success_files, failed_files)

        except Exception as e:
            messagebox.showerror("錯誤", f"步驟5檢查時發生錯誤：{str(e)}")
            self.file_handler.log_message(f"步驟5檢查錯誤: {e}")

    def _show_step5_results(self, success_files, failed_files):
        """顯示步驟5檢查結果"""
        if success_files:
            result_msg = f"✅ 步驟5檢查完成！成功處理 {len(success_files)} 個檔案\n\n"

            for original_file, output_file in success_files:
                filename = os.path.basename(original_file)
                output_filename = os.path.basename(output_file)
                result_msg += f"📁 {filename}\n"
                result_msg += f"   → {output_filename}\n"
                result_msg += f"\n執行步驟:\n步驟1: 檔案讀取與預處理 (1.1-1.4)\n步驟4: 格式檢測與分流 (4.1-4.3)\n步驟5: 格式轉換檢查 (5.1-5.6)\n"
                result_msg += f"檢查內容:\n- 5.1 找到標題行\n- 5.2 刪除標題行之前的行\n- 5.3 處理行列操作\n- 5.4 設置標準標題\n- 5.5 提取工作表信息\n- 5.6 生成Serial#和Bin#\n\n"

            if failed_files:
                result_msg += f"\n❌ 失敗檔案 ({len(failed_files)} 個):\n"
                for failed_file in failed_files:
                    result_msg += f"• {os.path.basename(failed_file)}\n"

            messagebox.showinfo("步驟5檢查結果", result_msg)
        else:
            messagebox.showerror("步驟5檢查失敗", "所有檔案都檢查失敗，請檢查檔案格式是否為CTA8280格式")

    def on_closing(self):
        """程式關閉時的清理工作"""
        # 清理所有臨時目錄
        for temp_dir in self.temp_dirs:
            if temp_dir:
                self.file_handler.cleanup_temp_dir(temp_dir)
        self.temp_dirs.clear()

        # 關閉主視窗
        self.root.destroy()
