#!/usr/bin/env python3
"""
測試ZIP檔案Summary sheet中原始檔案路徑修正
驗證修改後的程式是否正確處理ZIP檔案的原始路徑
"""

import os
import sys

# 添加專案根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_zip_original_filename_logic():
    """測試ZIP檔案原始檔案名稱邏輯"""
    print("=== 測試ZIP檔案原始檔案名稱邏輯 ===")

    # 模擬檔案信息結構
    test_cases = [
        {
            'name': 'ZIP檔案',
            'file_info': {
                'original_path': 'C:/test/data.csv.zip',
                'processed_path': 'C:/temp/extracted/data.csv',
                'temp_dir': 'C:/temp/extracted'
            },
            'expected_original': 'C:/test/data.csv.zip'
        },
        {
            'name': '普通CSV檔案',
            'file_info': {
                'original_path': 'C:/test/data.csv',
                'processed_path': 'C:/test/data.csv',
                'temp_dir': None
            },
            'expected_original': 'C:/test/data.csv'
        }
    ]

    for case in test_cases:
        print(f"\n測試案例: {case['name']}")
        file_info = case['file_info']

        # 模擬GUI中的邏輯
        original_zip_file = None
        if file_info['temp_dir']:
            original_zip_file = file_info['original_path']

        # 模擬FullConverter中的邏輯
        original_filename = original_zip_file if original_zip_file else file_info['processed_path']

        print(f"  原始路徑: {file_info['original_path']}")
        print(f"  處理路徑: {file_info['processed_path']}")
        print(f"  臨時目錄: {file_info['temp_dir']}")
        print(f"  傳遞給轉換器的original_zip_file: {original_zip_file}")
        print(f"  Summary中使用的original_filename: {original_filename}")
        print(f"  預期結果: {case['expected_original']}")
        print(f"  結果正確: {original_filename == case['expected_original']}")

if __name__ == "__main__":
    test_zip_original_filename_logic()