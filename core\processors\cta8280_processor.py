#!/usr/bin/env python3
"""
CTA8280格式處理器模組
"""

import time
from .base_processor import BaseProcessor
from utils.excel_utils import get_excel_handler
from config.settings import (
    SEARCH_KEYWORDS, CTA8280_HEADERS, FILL_EMPTY_SETTINGS,
    PROCESSING_LIMITS, TesterType
)


class CTA8280Processor(BaseProcessor):
    """CTA8280格式處理器 - 專門負責步驟5：CTA格式轉換為TMT格式"""

    def __init__(self):
        super().__init__()
        self.excel_handler = get_excel_handler()
        self.my_tester_type = TesterType.CTA8280
    
    def apply_format(self, ws_data11, ws_sum):
        """應用CTA8280格式轉換 - 步驟5：CTA轉TMT格式"""
        overall_start = time.time()
        self.file_handler.log_message("🚀 步驟5：開始CTA格式轉換為TMT格式...")

        # 步驟1: 找到標題行
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟1：開始找到標題行...")
        header_row = self._find_header_row(ws_data11)
        if header_row is None:
            self.file_handler.log_message("❌ 步驟1：未找到標題行，處理終止")
            return
        self._log_timing("步驟1-找到標題行", start_time)

        # 步驟2: 刪除標題行之前的行
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟2：開始刪除標題行之前的行...")
        self._remove_rows_before_header(ws_data11, header_row)
        self._log_timing("步驟2-刪除標題行之前的行", start_time)

        # 步驟3: 處理行列操作
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟3：開始處理行列操作...")
        my_found_row_n, my_column_n = self._process_row_column_operations(ws_data11)
        self._log_timing("步驟3-處理行列操作", start_time)

        # 步驟4: 設置CTA8280標準標題
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟4：開始設置CTA8280標準標題...")
        self._setup_headers_unified(ws_data11)
        self._log_timing("步驟4-設置CTA8280標準標題", start_time)

        # 步驟5: 從sum工作表提取信息
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟5：開始提取sum工作表信息...")
        self.excel_handler.extract_sum_info(ws_sum, ws_data11)
        self._log_timing("步驟5-提取sum工作表信息", start_time)

        # 步驟6: 生成Serial#和Bin#
        start_time = time.time()
        self.file_handler.log_message("🔄 步驟6：開始生成Serial#和Bin#...")
        self._generate_serial_bin_optimized(ws_data11)
        self._log_timing("步驟6-生成Serial#和Bin#", start_time)

        self._log_timing("步驟5 CTA轉TMT格式轉換總時間", overall_start)
        self.file_handler.log_message("✅ 步驟5：CTA轉TMT格式轉換完成")

    def _find_header_row(self, ws):
        """找到標題行"""
        for row_num in range(1, min(PROCESSING_LIMITS['HEADER_SEARCH_ROWS'], ws.max_row + 1)):
            cell_value = ws.cell(row_num, 1).value
            if cell_value and SEARCH_KEYWORDS['INDEX_NO'] in str(cell_value):
                self.file_handler.log_message(f"找到標題行在第{row_num}行")
                return row_num
        return None
    
    def _remove_rows_before_header(self, ws, header_row):
        """刪除標題行之前的行"""
        if header_row > 2:
            for _ in range(header_row - 1):
                ws.delete_rows(1)
            self.file_handler.log_message(f"刪除了前{header_row - 1}行")
    
    def _process_row_column_operations(self, ws):
        """處理行列操作（大文件優化版）"""
        # 清空A1
        ws.cell(1, 1).value = ""

        # 優化：使用更高效的搜索方法，限制搜索範圍
        keywords = SEARCH_KEYWORDS['DATA_NUM'] + [SEARCH_KEYWORDS['TEST_NUM']]
        my_found_row_n = 1
        my_column_n = self._find_column_by_keywords_fast(ws, 1, keywords, max_col=20)

        # 如果第1行沒找到，查找第2行
        if my_column_n is None:
            my_found_row_n = 2
            my_column_n = self._find_column_by_keywords_fast(ws, 2, keywords, max_col=20)

        # 如果都沒找到，使用預設值
        if my_column_n is None:
            my_column_n = min(ws.max_column, 50)
            self.file_handler.log_message("警告：未找到Data_Num/Data_Cnt/TEST_NUM，使用預設值")

        self.file_handler.log_message(f"myFindedRowN={my_found_row_n}, myColumnN={my_column_n}")

        # 執行行操作和插入操作
        self._perform_row_operations(ws, my_found_row_n)
        self._insert_header_rows(ws, my_found_row_n)

        return my_found_row_n, my_column_n

    # 注意：_find_column_by_keywords_fast 方法已移到 BaseProcessor 中

    def _perform_row_operations(self, ws, my_found_row_n):
        """執行行操作"""
        # VBA行操作的Python實現
        row_to_cut = my_found_row_n + 3
        row_to_insert = my_found_row_n + 2
        if row_to_cut <= ws.max_row:
            row_data = [cell.value for cell in ws[row_to_cut]]
            ws.insert_rows(row_to_insert)
            for col, value in enumerate(row_data, start=1):
                ws.cell(row=row_to_insert, column=col).value = value
            ws.delete_rows(row_to_cut + 1)

        row_to_cut = my_found_row_n + 1
        row_to_insert = my_found_row_n + 4
        if row_to_cut <= ws.max_row:
            row_data = [cell.value for cell in ws[row_to_cut]]
            ws.insert_rows(row_to_insert)
            for col, value in enumerate(row_data, start=1):
                ws.cell(row=row_to_insert, column=col).value = value
            ws.delete_rows(row_to_cut)

        # 條件刪除
        if self.my_tester_type == 2:
            ws.delete_rows(my_found_row_n + 4)

        # 插入單行
        ws.insert_rows(my_found_row_n + 1)

    def _insert_header_rows(self, ws, my_found_row_n):
        """插入標題行（優化版）"""
        import time
        start_time = time.time()

        rows_to_insert = 8 - my_found_row_n
        if rows_to_insert > 0:
            # 優化8: 批量插入行而不是逐行插入
            self._batch_insert_rows_optimized(ws, rows_to_insert)

            # 記錄優化效果
            end_time = time.time()
            self.file_handler.log_message(f"插入了{rows_to_insert}行在最前面")
            self.file_handler.log_message(f"⚡ 標題行插入優化完成，耗時{(end_time-start_time)*1000:.1f}ms")

    def _batch_insert_rows_optimized(self, ws, num_rows):
        """超級優化的批量插入行方法"""
        if num_rows <= 0:
            return

        import time
        start_time = time.time()

        try:
            # 超級優化4: 智能批量插入策略
            if num_rows <= 10:
                # 小量插入：使用批量插入
                ws.insert_rows(1, num_rows)
            else:
                # 大量插入：分批處理以避免記憶體問題
                batch_size = 5
                remaining = num_rows
                while remaining > 0:
                    current_batch = min(batch_size, remaining)
                    ws.insert_rows(1, current_batch)
                    remaining -= current_batch

        except (TypeError, AttributeError):
            # 降級到逐行插入，但使用優化策略
            self._fallback_insert_rows_optimized(ws, num_rows)

        # 記錄優化效果
        end_time = time.time()
        if hasattr(self, 'file_handler') and self.file_handler:
            self.file_handler.log_message(f"⚡ 超級批量插入{num_rows}行完成，耗時{(end_time-start_time)*1000:.1f}ms")

    def _fallback_insert_rows_optimized(self, ws, num_rows):
        """降級的優化插入行方法"""
        # 減少函數調用開銷的逐行插入
        insert_func = ws.insert_rows
        for _ in range(num_rows):
            insert_func(1)

        # 插入A、B列
        ws.insert_cols(1, 2)
        self.file_handler.log_message(f"步驟後總行數: {ws.max_row}, 總列數: {ws.max_column}")

    def _setup_headers_unified(self, ws):
        """統一的標題設置方法（合併原來的步驟4和6）"""
        # 步驟4: 應用CTA8280標題格式
        self.excel_handler.apply_cta8280_headers(ws)

        # 修正CSV檔案問題：清空不應該有資料的位置
        self._clean_unwanted_data_positions(ws)

        # 步驟6: 設置列標題
        # 清空第10、11行的C列到P列
        for col in range(3, 16):
            ws.cell(10, col).value = None
            ws.cell(11, col).value = None

        # 設置第12行C列以後的所有列為'12'（而不是'Unit'）
        for col in range(3, ws.max_column):
            if ws.cell(12, col).value is None or str(ws.cell(12, col).value).strip() == '':
                ws.cell(12, col).value = 'Unit'

    def _clean_unwanted_data_positions(self, ws):
        """清空CSV檔案轉換時不應該有資料的位置"""
        self.file_handler.log_message("🧹 開始清空CSV檔案的多餘資料...")

        # 清空A8 B8 A10 B10 A11 B11位置的多餘資料
        unwanted_positions = [
            (8, 1), (8, 2),  # A8, B8
            (10, 1), (10, 2),  # A10, B10
            (11, 1), (11, 2)   # A11, B11
        ]

        cleared_count = 0
        for row, col in unwanted_positions:
            current_value = ws.cell(row, col).value
            if current_value is not None and str(current_value).strip():
                self.file_handler.log_message(f"清空位置 {chr(64+col)}{row}: \"{current_value}\"")
                ws.cell(row, col).value = None
                cleared_count += 1

        if cleared_count > 0:
            self.file_handler.log_message(f"✅ 清空了{cleared_count}個多餘資料位置")
        else:
            self.file_handler.log_message("ℹ️ 沒有發現需要清空的多餘資料")

        # 清空最後一列的多餘資料（如果最後一列只有少量數據）
        self._clean_last_column_if_sparse(ws)

    def _clean_last_column_if_sparse(self, ws):
        """清空最後一列的稀疏資料"""
        max_col = ws.max_column
        if max_col <= 14:  # 如果列數不多，不需要清理
            return

        # 檢查最後一列的資料密度
        data_count = 0
        total_rows = min(50, ws.max_row)  # 檢查前50行

        for row in range(1, total_rows + 1):
            cell_value = ws.cell(row, max_col).value
            if cell_value is not None and str(cell_value).strip():
                data_count += 1

        # 如果最後一列的資料密度很低（少於20%），清空它
        data_density = data_count / total_rows if total_rows > 0 else 0

        if data_density < 0.2:  # 資料密度低於20%
            cleared_count = 0
            for row in range(1, ws.max_row + 1):
                cell_value = ws.cell(row, max_col).value
                if cell_value is not None and str(cell_value).strip():
                    ws.cell(row, max_col).value = None
                    cleared_count += 1

            if cleared_count > 0:
                self.file_handler.log_message(f"清空最後一列(第{max_col}列)的{cleared_count}個稀疏資料，資料密度: {data_density:.1%}")
        else:
            self.file_handler.log_message(f"最後一列資料密度正常({data_density:.1%})，保留資料")

    def _generate_serial_bin_optimized(self, ws):
        """生成Serial#和Bin#（優化版本，使用統一方法）"""
        # 使用統一的列搜索方法找到SW_Bin列
        sw_bin_col = self._find_column_by_keywords(ws, 8, [SEARCH_KEYWORDS['SW_BIN']], 20)
        if sw_bin_col:
            self.file_handler.log_message(f"找到SW_Bin列在第{sw_bin_col}列")

        # 使用優化的設備行遍歷方法生成Serial#和Bin#
        device_count = 0
        for row_num in self._iterate_device_rows_with_data(ws):
            device_count += 1
            ws.cell(row_num, 1).value = device_count

            if sw_bin_col:
                bin_value = ws.cell(row_num, sw_bin_col).value
                ws.cell(row_num, 2).value = bin_value if bin_value else 1
            else:
                ws.cell(row_num, 2).value = 1

        self.file_handler.log_message(f"處理了{device_count}個設備的數據")
