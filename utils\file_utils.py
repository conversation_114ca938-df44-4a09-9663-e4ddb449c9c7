#!/usr/bin/env python3
"""
檔案處理工具模組
"""

import pandas as pd
import logging
import zipfile
import tempfile
import shutil
from pathlib import Path
from config.settings import LOG_LEVEL, LOG_FORMAT, LOG_FILE

class FileHandler:
    """檔案處理類別"""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """設置日誌"""
        logging.basicConfig(
            level=LOG_LEVEL,
            format=LOG_FORMAT,
            handlers=[
                logging.FileHandler(LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def log_message(self, message):
        """記錄日誌"""
        logging.info(message)
    
    def read_csv_file(self, file_path):
        """讀取CSV文件"""
        try:
            self.log_message(f"讀取CSV文件: {file_path}")
            
            # 檢查文件是否存在
            if not Path(file_path).exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 先檢查文件的最大列數
            max_cols = 0
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                import csv
                reader = csv.reader(f)
                for row in reader:
                    max_cols = max(max_cols, len(row))
                    if max_cols > 2000:  # 避免檢查太多行
                        break
            
            # 使用pandas讀取，指定列數，保持空值為空字符串
            column_names = list(range(max_cols))
            df = pd.read_csv(file_path, dtype=str, header=None, names=column_names,
                           encoding='utf-8', on_bad_lines='skip',
                           keep_default_na=False, na_values=[])
            
            self.log_message(f"成功讀取CSV文件，大小: {df.shape}")
            return df
            
        except Exception as e:
            self.log_message(f"讀取CSV文件失敗: {e}")
            return None
    
    def generate_output_filename(self, input_file, suffix, output_dir=None):
        """生成輸出文件名

        Args:
            input_file: 輸入檔案路徑
            suffix: 檔案後綴
            output_dir: 輸出目錄（可選）
        """
        file_path = Path(input_file)

        # 生成基本檔案名
        if file_path.suffix.lower() == '.csv':
            base_filename = file_path.name.replace('.csv', suffix)
        elif file_path.suffix.lower() == '.spd':
            base_filename = file_path.name.replace('.spd', suffix)
        else:
            # 對於其他格式，直接添加後綴
            base_filename = f"{file_path.stem}{suffix}"

        # 決定輸出路徑
        if output_dir:
            return str(Path(output_dir) / base_filename)
        else:
            # 如果沒有指定輸出目錄，使用輸入檔案的目錄
            return str(file_path.parent / base_filename)
    
    def validate_file(self, file_path):
        """驗證檔案格式（支援CSV和SPD）"""
        try:
            # 基本檢查
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in ['.csv', '.spd']:
                return False, "不支援的檔案格式，僅支援CSV和SPD檔案"

            if not Path(file_path).exists():
                return False, "檔案不存在"

            # 嘗試讀取前幾行
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if len(lines) < 5:
                    return False, "檔案內容太少"

            return True, "檔案格式正確"

        except Exception as e:
            return False, f"檔案驗證失敗: {e}"

    def validate_csv_file(self, file_path):
        """驗證CSV文件格式（向後相容）"""
        return self.validate_file(file_path)

    def is_spd_file(self, file_path):
        """檢查是否為SPD檔案（通過副檔名或內容檢測）"""
        # 首先檢查副檔名
        if Path(file_path).suffix.lower() == '.spd':
            return True

        # 如果是CSV檔案，檢查內容是否為SPD格式
        if Path(file_path).suffix.lower() == '.csv':
            return self._is_spd_content(file_path)

        return False

    def _is_spd_content(self, file_path):
        """檢查CSV檔案內容是否為SPD格式"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                # 讀取前幾行來檢測SPD格式特徵
                lines = []
                for i, line in enumerate(f):
                    lines.append(line.strip())
                    if i >= 20:  # 只檢查前20行
                        break

            # SPD格式特徵檢測
            spd_indicators = 0

            # 檢查是否有典型的SPD格式標記
            for line in lines:
                # SPD檔案通常有這些特徵
                if any(indicator in line.upper() for indicator in [
                    'PART_ID', 'WAFER_ID', 'X_COORD', 'Y_COORD',
                    'TEST_T', 'SITE_NUM', 'HW_BIN', 'SW_BIN'
                ]):
                    spd_indicators += 1

                # 檢查是否有數字,數字,數字的格式（典型的SPD數據行）
                if ',' in line and len(line.split(',')) > 5:
                    parts = line.split(',')
                    numeric_parts = 0
                    for part in parts[:8]:  # 檢查前8個欄位
                        try:
                            float(part.strip())
                            numeric_parts += 1
                        except:
                            pass
                    if numeric_parts >= 4:  # 如果前8個欄位中有4個以上是數字
                        spd_indicators += 1

            # 如果有足夠的SPD格式指標，認為是SPD內容
            return spd_indicators >= 3

        except Exception as e:
            self.log_message(f"檢測SPD內容時出錯: {e}")
            return False

    def detect_file_type(self, file_path):
        """檢測檔案類型（通過內容和副檔名）"""
        # 使用改進的SPD檢測邏輯
        if self.is_spd_file(file_path):
            return 'spd'

        file_ext = Path(file_path).suffix.lower()
        if file_ext == '.csv':
            return 'csv'
        else:
            return 'unknown'

    def convert_spd_to_csv(self, spd_file_path):
        """將SPD檔案轉換為CSV格式，並清理數值格式"""
        try:
            self.log_message(f"開始將SPD檔案轉換為CSV: {spd_file_path}")

            # 生成臨時CSV檔案名
            if spd_file_path.endswith('.spd'):
                csv_file_path = spd_file_path.replace('.spd', '_temp.csv')
            elif spd_file_path.endswith('.csv'):
                # 如果已經是CSV檔案（但內容是SPD格式），創建一個臨時副本
                base_name = spd_file_path.replace('.csv', '')
                csv_file_path = f"{base_name}_temp.csv"
            else:
                # 其他情況，添加_temp.csv後綴
                csv_file_path = f"{spd_file_path}_temp.csv"

            # 讀取並處理SPD檔案
            import csv
            with open(spd_file_path, 'r', encoding='utf-8', errors='ignore') as spd_file:
                reader = csv.reader(spd_file)

                with open(csv_file_path, 'w', encoding='utf-8', newline='') as csv_file:
                    writer = csv.writer(csv_file)

                    for row in reader:
                        # 處理每一行的數據，清理數值格式
                        cleaned_row = []
                        for cell in row:
                            cleaned_cell = self._clean_numeric_value(cell)
                            cleaned_row.append(cleaned_cell)
                        writer.writerow(cleaned_row)

            self.log_message(f"SPD轉CSV完成（已清理數值格式）: {csv_file_path}")
            return csv_file_path

        except Exception as e:
            self.log_message(f"SPD轉CSV失敗: {e}")
            return None

    def _clean_numeric_value(self, value):
        """清理數值格式：如果是整數就移除小數點，清除空白字符"""
        if not value or not isinstance(value, str):
            return value

        # 清除前後空白字符
        cleaned = value.strip()

        # 如果不是數字，直接返回
        if not cleaned:
            return cleaned

        try:
            # 嘗試轉換為浮點數
            numeric_value = float(cleaned)

            # 如果是整數（沒有小數部分），返回整數格式
            if numeric_value == int(numeric_value):
                return str(int(numeric_value))
            else:
                # 如果是小數，保持原格式但清除空白
                return cleaned

        except (ValueError, TypeError):
            # 如果不是數字，返回清理後的字符串
            return cleaned

    def cleanup_temp_file(self, file_path):
        """清理臨時檔案"""
        try:
            if file_path and Path(file_path).exists():
                Path(file_path).unlink()
                self.log_message(f"清理臨時檔案: {file_path}")
        except Exception as e:
            self.log_message(f"清理臨時檔案失敗: {e}")

    def is_zip_file(self, file_path):
        """檢查是否為ZIP檔案"""
        return file_path.lower().endswith('.zip')

    def extract_zip_file(self, zip_path):
        """解壓縮ZIP檔案並返回解壓後的檔案路徑

        Args:
            zip_path: ZIP檔案路徑

        Returns:
            tuple: (success, extracted_file_path, temp_dir)
            - success: 是否成功
            - extracted_file_path: 解壓後的檔案路徑
            - temp_dir: 臨時目錄路徑（需要手動清理）
        """
        try:
            self.log_message(f"開始解壓縮ZIP檔案: {zip_path}")

            # 檢查ZIP檔案是否存在
            if not Path(zip_path).exists():
                return False, None, None

            # 創建臨時目錄
            temp_dir = tempfile.mkdtemp(prefix="csv_converter_")
            self.log_message(f"創建臨時目錄: {temp_dir}")

            # 解壓縮ZIP檔案
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                self.log_message(f"ZIP檔案包含: {file_list}")

                # 查找CSV或SPD檔案
                target_file = None
                for file_name in file_list:
                    if file_name.lower().endswith(('.csv', '.spd')):
                        target_file = file_name
                        break

                if not target_file:
                    self.log_message("ZIP檔案中沒有找到CSV或SPD檔案")
                    shutil.rmtree(temp_dir)
                    return False, None, None

                # 解壓縮目標檔案
                zip_ref.extract(target_file, temp_dir)
                extracted_path = Path(temp_dir) / target_file

                self.log_message(f"成功解壓縮檔案: {extracted_path}")
                return True, str(extracted_path), temp_dir

        except Exception as e:
            self.log_message(f"解壓縮ZIP檔案失敗: {e}")
            if 'temp_dir' in locals():
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
            return False, None, None

    def cleanup_temp_dir(self, temp_dir):
        """清理臨時目錄"""
        try:
            if temp_dir and Path(temp_dir).exists():
                shutil.rmtree(temp_dir)
                self.log_message(f"清理臨時目錄: {temp_dir}")
        except Exception as e:
            self.log_message(f"清理臨時目錄失敗: {e}")

    def detect_file_type(self, file_path):
        """檢測檔案類型（包含ZIP檔案）"""
        file_path_lower = file_path.lower()

        if file_path_lower.endswith('.zip'):
            # 檢查ZIP檔案內容來確定類型
            try:
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    file_list = zip_ref.namelist()
                    for file_name in file_list:
                        if file_name.lower().endswith('.csv'):
                            return "csv_zip"
                        elif file_name.lower().endswith('.spd'):
                            return "spd_zip"
                return "zip"
            except:
                return "zip"
        elif file_path_lower.endswith('.csv'):
            return "csv"
        elif file_path_lower.endswith('.spd'):
            return "spd"
        else:
            return "unknown"

    def validate_file(self, file_path):
        """驗證檔案（包含ZIP檔案支援）"""
        try:
            if not Path(file_path).exists():
                return False, "檔案不存在"

            file_type = self.detect_file_type(file_path)

            if file_type in ["csv_zip", "spd_zip"]:
                # 驗證ZIP檔案內容
                try:
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        file_list = zip_ref.namelist()
                        if not any(f.lower().endswith(('.csv', '.spd')) for f in file_list):
                            return False, "ZIP檔案中沒有CSV或SPD檔案"
                    return True, "有效的ZIP檔案"
                except zipfile.BadZipFile:
                    return False, "無效的ZIP檔案"
            elif file_type in ["csv", "spd"]:
                return True, f"有效的{file_type.upper()}檔案"
            else:
                return False, "不支援的檔案格式"

        except Exception as e:
            return False, f"檔案驗證錯誤: {str(e)}"

def get_file_handler():
    """獲取文件處理器實例"""
    return FileHandler()
