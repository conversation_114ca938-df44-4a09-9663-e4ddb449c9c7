2025-07-24 17:10:11,214 - INFO - 開始完整轉換...
2025-07-24 17:10:11,215 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-24 17:10:11,277 - INFO - 成功讀取CSV文件，大小: (911, 110)
2025-07-24 17:10:11,279 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-24 17:10:11,279 - INFO - === 開始7步驟統一處理管道 ===
2025-07-24 17:10:11,280 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-24 17:10:11,280 - INFO - 創建TMT格式工作簿...
2025-07-24 17:10:11,947 - INFO - ⚡ 優化寫入完成: 911行 × 110列，耗時664.0ms
2025-07-24 17:10:11,948 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-24 17:10:11,949 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-24 17:10:11,950 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-24 17:10:11,950 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-24 17:10:11,950 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-24 17:10:11,950 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-24 17:10:11,950 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-24 17:10:11,951 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-24 17:10:11,951 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-24 17:10:11,951 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-24 17:10:11,951 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-24 17:10:11,951 - INFO - 🔍 開始統一數據收集...
2025-07-24 17:10:11,951 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-24 17:10:11,978 - INFO - 收集項目數據: 108個項目
2025-07-24 17:10:11,979 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-24 17:10:35,232 - INFO - 收集設備數據: 899個設備（已排除無測試數據的行）
2025-07-24 17:10:35,232 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-24 17:10:35,260 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-24 17:10:35,263 - INFO - 收集Site數據: 2個Site，899個設備有Site信息
2025-07-24 17:10:35,263 - INFO - 🔄 6.1.4 收集限制值...
2025-07-24 17:10:35,264 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-24 17:10:35,264 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-24 17:10:35,265 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-24 17:10:35,265 - INFO - ⏱️ 統一數據收集 執行時間: 23.31s
2025-07-24 17:10:35,265 - INFO - ✅ 統一數據收集完成: 項目108個, 設備899個, Site2個
2025-07-24 17:10:35,265 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 23.31s
2025-07-24 17:10:35,265 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-24 17:10:35,266 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-24 17:10:35,266 - INFO -   - 填充缺失的項目編號和名稱
2025-07-24 17:10:35,266 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:35,267 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 2.0ms
2025-07-24 17:10:35,267 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-24 17:10:35,267 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-24 17:10:35,267 - INFO -   - 填充預設限制值
2025-07-24 17:10:35,268 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:35,268 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: <1ms
2025-07-24 17:10:35,268 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-24 17:10:35,268 - INFO -   - 驗證數據完整性
2025-07-24 17:10:35,269 - INFO -   - 準備統一數據摘要
2025-07-24 17:10:35,269 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 23.32s
2025-07-24 17:10:35,269 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-24 17:10:35,269 - INFO - 應用Device2BinControl處理...
2025-07-24 17:10:35,269 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:35,269 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 17:10:35,269 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:35,269 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-24 17:10:35,270 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.0ms
2025-07-24 17:10:35,270 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-24 17:10:35,271 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:35,328 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:35,329 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 58.0ms
2025-07-24 17:10:35,329 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:35,330 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-24 17:10:35,330 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-24 17:10:35,330 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:35,330 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:35,331 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:35,331 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:35,331 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:35,332 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:35,332 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-24 17:10:35,333 - INFO - 收集原始Bin值: 899個設備
2025-07-24 17:10:35,988 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-24 17:10:35,990 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-24 17:10:35,991 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-24 17:10:35,991 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:35,991 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 661.0ms
2025-07-24 17:10:35,992 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:35,992 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:35,992 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:35,992 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:35,992 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:35,993 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:35,993 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:39,238 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-24 17:10:39,238 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-24 17:10:39,239 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 3.25s
2025-07-24 17:10:39,239 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 3.97s
2025-07-24 17:10:39,240 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:39,240 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-24 17:10:39,240 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:39,240 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:39,241 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:39,241 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:39,241 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:39,242 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-24 17:10:39,242 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:39,242 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:39,242 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:39,243 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:39,243 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:39,243 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:39,243 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:39,243 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 17:10:39,244 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:39,244 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-24 17:10:39,244 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 17:10:39,244 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-24 17:10:39,245 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:39,300 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:39,301 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 56.0ms
2025-07-24 17:10:39,302 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:39,302 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-24 17:10:39,303 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 2.0ms
2025-07-24 17:10:39,303 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:39,303 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:39,304 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:39,304 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:39,304 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:39,305 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:39,305 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-24 17:10:39,307 - INFO - 收集原始Bin值: 899個設備
2025-07-24 17:10:39,960 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-24 17:10:39,962 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-24 17:10:39,963 - INFO - VBA 457-469行：填充基本統計 Total=899, Pass=827, Fail=72, Yield=91.99%
2025-07-24 17:10:39,965 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-24 17:10:39,967 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-24 17:10:39,967 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=899
2025-07-24 17:10:39,968 - INFO - 創建Site統計完成: 2個Site
2025-07-24 17:10:39,969 - INFO -   Site 1: 439個設備
2025-07-24 17:10:39,969 - INFO -   Site 2: 460個設備
2025-07-24 17:10:39,969 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-24 17:10:39,969 - INFO - 填充Site 1統計完成: 439個設備
2025-07-24 17:10:39,970 - INFO - 填充Site 2統計完成: 460個設備
2025-07-24 17:10:39,970 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-24 17:10:39,998 - INFO - VBA 362-420行（修正版）：填充了6個測試項目的Bin數據，無重複
2025-07-24 17:10:39,999 - INFO - 設置 AutoFilter 範圍: A6:I12
2025-07-24 17:10:40,000 - INFO - 按 B 欄由大到小排序了 6 行資料
2025-07-24 17:10:40,001 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-24 17:10:40,001 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-24 17:10:40,001 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:40,002 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 699.1ms
2025-07-24 17:10:40,002 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:40,002 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:40,003 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:40,003 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:40,003 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:40,004 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:40,004 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:44,951 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-24 17:10:44,952 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-24 17:10:44,952 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 4.95s
2025-07-24 17:10:44,953 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 5.71s
2025-07-24 17:10:44,953 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:44,953 - INFO - === 統一處理管道完成 ===
2025-07-24 17:10:53,618 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-24 17:10:55,122 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx，耗時1502.4ms
2025-07-24 17:10:55,122 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx
2025-07-24 17:10:55,125 - INFO - 開始完整轉換...
2025-07-24 17:10:55,126 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-24 17:10:55,160 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-24 17:10:55,169 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-24 17:10:55,170 - INFO - === 開始7步驟統一處理管道 ===
2025-07-24 17:10:55,170 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-24 17:10:55,171 - INFO - 執行CTA到TMT格式轉換...
2025-07-24 17:10:57,088 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時1905.2ms
2025-07-24 17:10:57,104 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時14.9ms
2025-07-24 17:10:57,110 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時3.0ms
2025-07-24 17:10:57,111 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時1930.8ms
2025-07-24 17:10:57,111 - INFO - 應用CTA8280轉換處理...
2025-07-24 17:10:57,112 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-24 17:10:57,112 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-24 17:10:57,113 - INFO - 找到標題行在第2行
2025-07-24 17:10:57,113 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-24 17:10:57,113 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-24 17:10:57,114 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-24 17:10:57,114 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-24 17:10:57,116 - INFO - myFindedRowN=2, myColumnN=14
2025-07-24 17:10:57,155 - INFO - ⚡ 超級批量插入6行完成，耗時7.0ms
2025-07-24 17:10:57,155 - INFO - 插入了6行在最前面
2025-07-24 17:10:57,156 - INFO - ⚡ 標題行插入優化完成，耗時7.0ms
2025-07-24 17:10:57,157 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 43.0ms
2025-07-24 17:10:57,158 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-24 17:10:57,160 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-24 17:10:57,161 - INFO - 清空位置 A8: "Index_No"
2025-07-24 17:10:57,162 - INFO - 清空位置 B8: "Dut_No"
2025-07-24 17:10:57,163 - INFO - 清空位置 A10: "ASD"
2025-07-24 17:10:57,163 - INFO - 清空位置 B10: "QQ"
2025-07-24 17:10:57,164 - INFO - 清空位置 A11: "A"
2025-07-24 17:10:57,164 - INFO - 清空位置 B11: "B"
2025-07-24 17:10:57,165 - INFO - ✅ 清空了6個多餘資料位置
2025-07-24 17:10:57,166 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-24 17:10:57,167 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 9.0ms
2025-07-24 17:10:57,168 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-24 17:10:57,170 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 2.0ms
2025-07-24 17:10:57,170 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-24 17:10:57,172 - INFO - 找到SW_Bin列在第6列
2025-07-24 17:10:57,173 - INFO - 處理了17個設備的數據
2025-07-24 17:10:57,174 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 4.0ms
2025-07-24 17:10:57,174 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 62.0ms
2025-07-24 17:10:57,175 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-24 17:10:57,175 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-24 17:10:57,175 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-24 17:10:57,176 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-24 17:10:57,176 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-24 17:10:57,176 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-24 17:10:57,176 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-24 17:10:57,177 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-24 17:10:57,177 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-24 17:10:57,177 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-24 17:10:57,178 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-24 17:10:57,178 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-24 17:10:57,178 - INFO - 🔍 開始統一數據收集...
2025-07-24 17:10:57,179 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-24 17:10:57,180 - INFO - 收集項目數據: 58個項目
2025-07-24 17:10:57,180 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-24 17:10:57,190 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-24 17:10:57,190 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-24 17:10:57,192 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-24 17:10:57,192 - INFO - 收集Site數據: 4個Site，16個設備有Site信息
2025-07-24 17:10:57,193 - INFO - 🔄 6.1.4 收集限制值...
2025-07-24 17:10:57,194 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-24 17:10:57,194 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-24 17:10:57,195 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-24 17:10:57,196 - INFO - ⏱️ 統一數據收集 執行時間: 18.0ms
2025-07-24 17:10:57,196 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-24 17:10:57,197 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 19.0ms
2025-07-24 17:10:57,197 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-24 17:10:57,197 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-24 17:10:57,198 - INFO -   - 填充缺失的項目編號和名稱
2025-07-24 17:10:57,198 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:57,202 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 5.0ms
2025-07-24 17:10:57,202 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-24 17:10:57,203 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-24 17:10:57,203 - INFO -   - 填充預設限制值
2025-07-24 17:10:57,204 - INFO -   - 設置紅色字體標記
2025-07-24 17:10:57,210 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 7.0ms
2025-07-24 17:10:57,210 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-24 17:10:57,211 - INFO -   - 驗證數據完整性
2025-07-24 17:10:57,212 - INFO -   - 準備統一數據摘要
2025-07-24 17:10:57,213 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 35.0ms
2025-07-24 17:10:57,213 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-24 17:10:57,214 - INFO - 應用Device2BinControl處理...
2025-07-24 17:10:57,214 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:57,214 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-24 17:10:57,215 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:57,215 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-24 17:10:57,215 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 17:10:57,216 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-24 17:10:57,216 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:57,218 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:57,219 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 3.0ms
2025-07-24 17:10:57,219 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:57,220 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-24 17:10:57,220 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-24 17:10:57,220 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:57,221 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:57,221 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:57,222 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:57,222 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:57,223 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:57,223 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-24 17:10:57,224 - INFO - 收集原始Bin值: 17個設備
2025-07-24 17:10:57,236 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-24 17:10:57,237 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-24 17:10:57,238 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-24 17:10:57,238 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:57,239 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 19.0ms
2025-07-24 17:10:57,240 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:57,240 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:57,241 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:57,241 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:57,241 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:57,242 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:57,242 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:57,310 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-24 17:10:57,311 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-24 17:10:57,311 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 72.0ms
2025-07-24 17:10:57,312 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 98.0ms
2025-07-24 17:10:57,312 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:57,312 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-24 17:10:57,313 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:57,313 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:57,313 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:57,314 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:57,314 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:57,314 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-24 17:10:57,315 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:57,315 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:57,315 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:57,315 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:57,316 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:57,316 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:57,316 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-24 17:10:57,317 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: 1.0ms
2025-07-24 17:10:57,317 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-24 17:10:57,318 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-24 17:10:57,318 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-24 17:10:57,318 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-24 17:10:57,319 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-24 17:10:57,320 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-24 17:10:57,321 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 3.0ms
2025-07-24 17:10:57,321 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-24 17:10:57,322 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-24 17:10:57,322 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-24 17:10:57,323 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-24 17:10:57,323 - INFO -   - 7.1.1 收集原始Bin值
2025-07-24 17:10:57,324 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-24 17:10:57,324 - INFO -   - 7.1.3 計算Bin統計
2025-07-24 17:10:57,324 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-24 17:10:57,325 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-24 17:10:57,325 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-24 17:10:57,325 - INFO - 收集原始Bin值: 17個設備
2025-07-24 17:10:57,336 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-24 17:10:57,337 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-24 17:10:57,338 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-24 17:10:57,339 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-24 17:10:57,340 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-24 17:10:57,340 - INFO - Site信息檢查: total_site_no=4, good_site_n=True, site_data數量=16
2025-07-24 17:10:57,341 - INFO - 創建Site統計完成: 4個Site
2025-07-24 17:10:57,342 - INFO -   Site 1: 4個設備
2025-07-24 17:10:57,342 - INFO -   Site 2: 4個設備
2025-07-24 17:10:57,342 - INFO -   Site 3: 4個設備
2025-07-24 17:10:57,343 - INFO -   Site 4: 4個設備
2025-07-24 17:10:57,343 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-24 17:10:57,344 - INFO - 填充Site 1統計完成: 4個設備
2025-07-24 17:10:57,345 - INFO - 填充Site 2統計完成: 4個設備
2025-07-24 17:10:57,345 - INFO - 填充Site 3統計完成: 4個設備
2025-07-24 17:10:57,346 - INFO - 填充Site 4統計完成: 4個設備
2025-07-24 17:10:57,346 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-24 17:10:57,347 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-24 17:10:57,348 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-24 17:10:57,348 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-24 17:10:57,349 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-24 17:10:57,349 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-24 17:10:57,350 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-24 17:10:57,350 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 27.0ms
2025-07-24 17:10:57,350 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-24 17:10:57,351 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-24 17:10:57,351 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-24 17:10:57,352 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-24 17:10:57,352 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-24 17:10:57,352 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-24 17:10:57,353 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-24 17:10:57,442 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-24 17:10:57,442 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-24 17:10:57,443 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 93.0ms
2025-07-24 17:10:57,443 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 127.0ms
2025-07-24 17:10:57,443 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-24 17:10:57,444 - INFO - === 統一處理管道完成 ===
2025-07-24 17:11:01,993 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-24 17:11:02,521 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx，耗時528.0ms
2025-07-24 17:11:02,522 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx
2025-07-25 09:25:08,596 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-25 09:25:08,599 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_oii33k2k
2025-07-25 09:25:08,599 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-25 09:25:08,603 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_oii33k2k\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-25 09:25:08,606 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd.zip
2025-07-25 09:25:08,607 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_p8b7w617
2025-07-25 09:25:08,607 - INFO - ZIP檔案包含: ['G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd']
2025-07-25 09:25:08,639 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_p8b7w617\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-25 09:25:09,050 - INFO - 開始完整轉換...
2025-07-25 09:25:09,051 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_oii33k2k\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-25 09:25:09,155 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-25 09:25:09,156 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-25 09:25:09,156 - INFO - === 開始7步驟統一處理管道 ===
2025-07-25 09:25:09,157 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-25 09:25:09,157 - INFO - 執行CTA到TMT格式轉換...
2025-07-25 09:25:09,773 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時612.2ms
2025-07-25 09:25:10,058 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時282.6ms
2025-07-25 09:25:10,064 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時5.4ms
2025-07-25 09:25:10,065 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時906.8ms
2025-07-25 09:25:10,065 - INFO - 應用CTA8280轉換處理...
2025-07-25 09:25:10,065 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-25 09:25:10,065 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-25 09:25:10,068 - INFO - 找到標題行在第2行
2025-07-25 09:25:10,068 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 3.1ms
2025-07-25 09:25:10,068 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-25 09:25:10,069 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-25 09:25:10,069 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-25 09:25:10,081 - INFO - myFindedRowN=2, myColumnN=14
2025-07-25 09:25:11,066 - INFO - ⚡ 超級批量插入6行完成，耗時148.8ms
2025-07-25 09:25:11,067 - INFO - 插入了6行在最前面
2025-07-25 09:25:11,067 - INFO - ⚡ 標題行插入優化完成，耗時149.4ms
2025-07-25 09:25:11,068 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 998.8ms
2025-07-25 09:25:11,068 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-25 09:25:11,068 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-25 09:25:11,069 - INFO - 清空位置 A8: "Index_No"
2025-07-25 09:25:11,069 - INFO - 清空位置 B8: "Dut_No"
2025-07-25 09:25:11,069 - INFO - 清空位置 A10: "Max"
2025-07-25 09:25:11,069 - INFO - 清空位置 B10: "Max"
2025-07-25 09:25:11,069 - INFO - 清空位置 A11: "Min"
2025-07-25 09:25:11,070 - INFO - 清空位置 B11: "Min"
2025-07-25 09:25:11,070 - INFO - ✅ 清空了6個多餘資料位置
2025-07-25 09:25:11,088 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 20.3ms
2025-07-25 09:25:11,089 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-25 09:25:11,090 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: <1ms
2025-07-25 09:25:11,090 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-25 09:25:11,098 - INFO - 找到SW_Bin列在第6列
2025-07-25 09:25:11,105 - INFO - 處理了264個設備的數據
2025-07-25 09:25:11,105 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 15.3ms
2025-07-25 09:25:11,106 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 1.04s
2025-07-25 09:25:11,106 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-25 09:25:11,106 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-25 09:25:11,107 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-25 09:25:11,107 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-25 09:25:11,107 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-25 09:25:11,107 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-25 09:25:11,108 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-25 09:25:11,108 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-25 09:25:11,108 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-25 09:25:11,109 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-25 09:25:11,109 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-25 09:25:11,109 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-25 09:25:11,109 - INFO - 🔍 開始統一數據收集...
2025-07-25 09:25:11,110 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-25 09:25:11,117 - INFO - 收集項目數據: 197個項目
2025-07-25 09:25:11,118 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-25 09:25:12,467 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-25 09:25:12,468 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-25 09:25:12,473 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-25 09:25:12,474 - INFO - 收集Site數據: 1個Site，264筆Site資料
2025-07-25 09:25:12,474 - INFO - 🔄 6.1.4 收集限制值...
2025-07-25 09:25:12,475 - INFO - 收集限制值: Max 184個, Min 184個
2025-07-25 09:25:12,475 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-25 09:25:12,475 - INFO - 設置第6行項目編號: 從第3列開始，共197個項目
2025-07-25 09:25:12,476 - INFO - ⏱️ 統一數據收集 執行時間: 1.37s
2025-07-25 09:25:12,476 - INFO - ✅ 統一數據收集完成: 項目197個, 設備264個, Site1個
2025-07-25 09:25:12,476 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 1.37s
2025-07-25 09:25:12,476 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-25 09:25:12,476 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-25 09:25:12,477 - INFO -   - 填充缺失的項目編號和名稱
2025-07-25 09:25:12,477 - INFO -   - 設置紅色字體標記
2025-07-25 09:25:12,482 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 6.1ms
2025-07-25 09:25:12,483 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-25 09:25:12,483 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-25 09:25:12,484 - INFO -   - 填充預設限制值
2025-07-25 09:25:12,484 - INFO -   - 設置紅色字體標記
2025-07-25 09:25:12,489 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 5.5ms
2025-07-25 09:25:12,489 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-25 09:25:12,490 - INFO -   - 驗證數據完整性
2025-07-25 09:25:12,490 - INFO -   - 準備統一數據摘要
2025-07-25 09:25:12,490 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-25 09:25:12,491 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-25 09:25:12,491 - INFO - 開始重新計算設備Bin值...
2025-07-25 09:25:12,532 - INFO - 重新計算並更新了264個設備的Bin值
2025-07-25 09:25:12,532 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 41.8ms
2025-07-25 09:25:12,533 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 1.42s
2025-07-25 09:25:12,534 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-25 09:25:12,534 - INFO - 應用Device2BinControl處理...
2025-07-25 09:25:12,535 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:25:12,535 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:25:12,535 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:25:12,536 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-25 09:25:12,536 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:25:12,536 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-25 09:25:12,536 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:25:12,550 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:25:12,551 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 14.5ms
2025-07-25 09:25:12,552 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:25:12,552 - INFO - ✅ 數據摘要: 項目197個, 設備264個, Site1個
2025-07-25 09:25:12,553 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.2ms
2025-07-25 09:25:12,554 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:25:12,554 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:25:12,554 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:25:12,554 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:25:12,554 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:25:12,555 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:25:12,555 - INFO - 使用統一數據: 設備264個, 項目197個
2025-07-25 09:25:12,555 - INFO - 收集原始Bin值: 264個設備
2025-07-25 09:25:12,643 - INFO - 應用染色邏輯: 63個設備的失敗項目
2025-07-25 09:25:12,644 - INFO - Bin統計: Pass=201, Fail=63, Total=264
2025-07-25 09:25:12,644 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-25 09:25:12,645 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:25:12,645 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 91.7ms
2025-07-25 09:25:12,646 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:25:12,646 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:25:12,646 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:25:12,646 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:25:12,646 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:25:12,647 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:25:12,647 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:25:13,024 - INFO - ⚡ 超級批量字體設置完成，總共處理52126個cell
2025-07-25 09:25:13,024 - INFO - 設置數據區域字體顏色: 行13-276, 列2-199
2025-07-25 09:25:13,025 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 379.1ms
2025-07-25 09:25:13,025 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 490.4ms
2025-07-25 09:25:13,025 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:25:13,026 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-25 09:25:13,026 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-25 09:25:13,026 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:25:13,026 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:25:13,027 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:25:13,027 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:25:13,027 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-25 09:25:13,028 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:25:13,028 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:25:13,028 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:25:13,028 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:25:13,029 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:25:13,029 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:25:13,029 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:25:13,030 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:25:13,030 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:25:13,030 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-25 09:25:13,031 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:25:13,031 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-25 09:25:13,031 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:25:13,046 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:25:13,047 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 15.1ms
2025-07-25 09:25:13,047 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:25:13,047 - INFO - ✅ 數據摘要: 項目197個, 設備264個, Site1個
2025-07-25 09:25:13,047 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-25 09:25:13,048 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:25:13,048 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:25:13,049 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:25:13,049 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:25:13,049 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:25:13,050 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:25:13,050 - INFO - 使用統一數據: 設備264個, 項目197個
2025-07-25 09:25:13,050 - INFO - 收集原始Bin值: 264個設備
2025-07-25 09:25:13,141 - INFO - 應用染色邏輯: 63個設備的失敗項目
2025-07-25 09:25:13,141 - INFO - Bin統計: Pass=201, Fail=63, Total=264
2025-07-25 09:25:13,142 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=201, Fail=63, Yield=76.14%
2025-07-25 09:25:13,143 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_oii33k2k\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-25 09:25:13,143 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-25 09:25:13,144 - INFO - Site信息檢查: total_site_no=1, good_site_n=True, site_data數量=264
2025-07-25 09:25:13,144 - INFO - 創建Site統計完成: 1個Site
2025-07-25 09:25:13,144 - INFO -   Site 1: 264個設備
2025-07-25 09:25:13,144 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-25 09:25:13,145 - INFO - 填充Site 1統計完成: 264個設備
2025-07-25 09:25:13,145 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-25 09:25:13,151 - INFO - VBA 362-420行（修正版）：填充了16個測試項目的Bin數據，無重複
2025-07-25 09:25:13,152 - INFO - 設置 AutoFilter 範圍: A6:G22
2025-07-25 09:25:13,152 - INFO - 按 B 欄由大到小排序了 16 行資料
2025-07-25 09:25:13,153 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-25 09:25:13,153 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-25 09:25:13,153 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:25:13,154 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 106.0ms
2025-07-25 09:25:13,154 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:25:13,155 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:25:13,155 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:25:13,156 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:25:13,156 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:25:13,157 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:25:13,157 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:25:13,839 - INFO - ⚡ 超級批量字體設置完成，總共處理52126個cell
2025-07-25 09:25:13,839 - INFO - 設置數據區域字體顏色: 行13-276, 列2-199
2025-07-25 09:25:13,840 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 685.5ms
2025-07-25 09:25:13,840 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 811.0ms
2025-07-25 09:25:13,841 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:25:13,841 - INFO - === 統一處理管道完成 ===
2025-07-25 09:25:17,460 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-25 09:25:18,619 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時1159.1ms
2025-07-25 09:25:18,620 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-25 09:25:18,621 - INFO - 開始完整轉換...
2025-07-25 09:25:18,621 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_p8b7w617\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-25 09:25:18,807 - INFO - 成功讀取CSV文件，大小: (7971, 110)
2025-07-25 09:25:18,807 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-25 09:25:18,808 - INFO - === 開始7步驟統一處理管道 ===
2025-07-25 09:25:18,808 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-25 09:25:18,808 - INFO - 創建TMT格式工作簿...
2025-07-25 09:25:20,646 - INFO - ⚡ 優化寫入完成: 7971行 × 110列，耗時1836.6ms
2025-07-25 09:25:20,650 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-25 09:25:20,650 - INFO - TMT格式不需要sum分頁
2025-07-25 09:25:20,651 - INFO - TMT工作簿創建完成：僅Data11工作表 7971行，無sum分頁
2025-07-25 09:25:20,651 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-25 09:25:20,651 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-25 09:25:20,651 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-25 09:25:20,651 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-25 09:25:20,652 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-25 09:25:20,652 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-25 09:25:20,652 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-25 09:25:20,653 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-25 09:25:20,653 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-25 09:25:20,653 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-25 09:25:20,653 - INFO - 🔍 開始統一數據收集...
2025-07-25 09:25:20,654 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-25 09:25:20,684 - INFO - 收集項目數據: 108個項目
2025-07-25 09:25:20,684 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-25 09:29:54,010 - INFO - 收集設備數據: 7959個設備（已排除無測試數據的行）
2025-07-25 09:29:54,011 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-25 09:29:54,041 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-25 09:29:54,050 - INFO - 收集Site數據: 2個Site，7959筆Site資料
2025-07-25 09:29:54,051 - INFO - 🔄 6.1.4 收集限制值...
2025-07-25 09:29:54,051 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-25 09:29:54,051 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-25 09:29:54,052 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-25 09:29:54,052 - INFO - ⏱️ 統一數據收集 執行時間: 273.40s
2025-07-25 09:29:54,052 - INFO - ✅ 統一數據收集完成: 項目108個, 設備7959個, Site2個
2025-07-25 09:29:54,052 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 273.40s
2025-07-25 09:29:54,053 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-25 09:29:54,053 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-25 09:29:54,053 - INFO -   - 填充缺失的項目編號和名稱
2025-07-25 09:29:54,053 - INFO -   - 設置紅色字體標記
2025-07-25 09:29:54,054 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 1.3ms
2025-07-25 09:29:54,054 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-25 09:29:54,055 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-25 09:29:54,055 - INFO -   - 填充預設限制值
2025-07-25 09:29:54,055 - INFO -   - 設置紅色字體標記
2025-07-25 09:29:54,056 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 1.5ms
2025-07-25 09:29:54,056 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-25 09:29:54,056 - INFO -   - 驗證數據完整性
2025-07-25 09:29:54,057 - INFO -   - 準備統一數據摘要
2025-07-25 09:29:54,057 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-25 09:29:54,058 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-25 09:29:54,058 - INFO - 開始重新計算設備Bin值...
2025-07-25 09:29:54,769 - INFO - 重新計算並更新了7959個設備的Bin值
2025-07-25 09:29:54,769 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 711.4ms
2025-07-25 09:29:54,769 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 274.12s
2025-07-25 09:29:54,770 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-25 09:29:54,770 - INFO - 應用Device2BinControl處理...
2025-07-25 09:29:54,770 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:29:54,771 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:29:54,771 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:29:54,771 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-25 09:29:54,771 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:29:54,772 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-25 09:29:54,772 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:29:54,853 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:29:54,854 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 81.7ms
2025-07-25 09:29:54,854 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:29:54,855 - INFO - ✅ 數據摘要: 項目108個, 設備7959個, Site2個
2025-07-25 09:29:54,855 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-25 09:29:54,855 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:29:54,856 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:29:54,856 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:29:54,857 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:29:54,857 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:29:54,857 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:29:54,858 - INFO - 使用統一數據: 設備7959個, 項目108個
2025-07-25 09:29:54,861 - INFO - 收集原始Bin值: 7959個設備
2025-07-25 09:29:56,467 - INFO - 應用染色邏輯: 1329個設備的失敗項目
2025-07-25 09:29:56,468 - INFO - Bin統計: Pass=6630, Fail=1329, Total=7959
2025-07-25 09:29:56,468 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-25 09:29:56,469 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:29:56,469 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.61s
2025-07-25 09:29:56,470 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:29:56,470 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:29:56,470 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:29:56,470 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:29:56,471 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:29:56,471 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:29:56,471 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:29:57,173 - INFO - ⚡ 已處理91000個cell的字體顏色
2025-07-25 09:29:57,212 - INFO - ⚡ 已處理96000個cell的字體顏色
2025-07-25 09:29:59,912 - INFO - ⚡ 已處理421000個cell的字體顏色
2025-07-25 09:30:00,779 - INFO - ⚡ 已處理515000個cell的字體顏色
2025-07-25 09:30:01,296 - INFO - ⚡ 已處理581000個cell的字體顏色
2025-07-25 09:30:03,719 - INFO - ⚡ 超級批量字體設置完成，總共處理866089個cell
2025-07-25 09:30:03,720 - INFO - 設置數據區域字體顏色: 行13-7971, 列2-110
2025-07-25 09:30:03,720 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 7.25s
2025-07-25 09:30:03,721 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 8.95s
2025-07-25 09:30:03,721 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:30:03,721 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-25 09:30:03,722 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:03,722 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:03,722 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:03,722 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:03,723 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:03,723 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-25 09:30:03,723 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:03,723 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:03,724 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:03,724 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:03,724 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:03,725 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:03,725 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:30:03,725 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:30:03,726 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:30:03,726 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-25 09:30:03,727 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.0ms
2025-07-25 09:30:03,727 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-25 09:30:03,728 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:30:03,807 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:30:03,808 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 80.2ms
2025-07-25 09:30:03,808 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:30:03,809 - INFO - ✅ 數據摘要: 項目108個, 設備7959個, Site2個
2025-07-25 09:30:03,809 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-25 09:30:03,809 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:03,810 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:03,810 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:03,810 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:03,811 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:03,811 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:30:03,811 - INFO - 使用統一數據: 設備7959個, 項目108個
2025-07-25 09:30:03,813 - INFO - 收集原始Bin值: 7959個設備
2025-07-25 09:30:05,399 - INFO - 應用染色邏輯: 1329個設備的失敗項目
2025-07-25 09:30:05,400 - INFO - Bin統計: Pass=6630, Fail=1329, Total=7959
2025-07-25 09:30:05,400 - INFO - VBA 457-469行：填充基本統計 Total=7959, Pass=6630, Fail=1329, Yield=83.30%
2025-07-25 09:30:05,402 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_p8b7w617\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-25 09:30:05,403 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-25 09:30:05,403 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=7959
2025-07-25 09:30:05,405 - INFO - 創建Site統計完成: 2個Site
2025-07-25 09:30:05,405 - INFO -   Site 1: 3758個設備
2025-07-25 09:30:05,406 - INFO -   Site 2: 4201個設備
2025-07-25 09:30:05,406 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-25 09:30:05,406 - INFO - 填充Site 1統計完成: 3758個設備
2025-07-25 09:30:05,407 - INFO - 填充Site 2統計完成: 4201個設備
2025-07-25 09:30:05,407 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-25 09:30:05,442 - INFO - VBA 362-420行（修正版）：填充了17個測試項目的Bin數據，無重複
2025-07-25 09:30:05,443 - INFO - 設置 AutoFilter 範圍: A6:I23
2025-07-25 09:30:05,444 - INFO - 按 B 欄由大到小排序了 17 行資料
2025-07-25 09:30:05,444 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-25 09:30:05,444 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-25 09:30:05,444 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:30:05,445 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.64s
2025-07-25 09:30:05,445 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:30:05,446 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:05,446 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:05,446 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:05,446 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:05,447 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:05,447 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:06,762 - INFO - ⚡ 已處理91000個cell的字體顏色
2025-07-25 09:30:06,834 - INFO - ⚡ 已處理96000個cell的字體顏色
2025-07-25 09:30:11,447 - INFO - ⚡ 已處理421000個cell的字體顏色
2025-07-25 09:30:12,876 - INFO - ⚡ 已處理515000個cell的字體顏色
2025-07-25 09:30:13,855 - INFO - ⚡ 已處理581000個cell的字體顏色
2025-07-25 09:30:17,979 - INFO - ⚡ 超級批量字體設置完成，總共處理866089個cell
2025-07-25 09:30:17,980 - INFO - 設置數據區域字體顏色: 行13-7971, 列2-110
2025-07-25 09:30:17,980 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 12.53s
2025-07-25 09:30:17,980 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 14.26s
2025-07-25 09:30:17,981 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:30:17,981 - INFO - === 統一處理管道完成 ===
2025-07-25 09:30:37,182 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-25 09:30:43,962 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300_spd_converted.xlsx，耗時6779.8ms
2025-07-25 09:30:43,962 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300_spd_converted.xlsx
2025-07-25 09:30:43,964 - INFO - 開始完整轉換...
2025-07-25 09:30:43,964 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-25 09:30:44,009 - INFO - 成功讀取CSV文件，大小: (911, 110)
2025-07-25 09:30:44,010 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-25 09:30:44,010 - INFO - === 開始7步驟統一處理管道 ===
2025-07-25 09:30:44,010 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-25 09:30:44,011 - INFO - 創建TMT格式工作簿...
2025-07-25 09:30:44,157 - INFO - ⚡ 優化寫入完成: 911行 × 110列，耗時145.6ms
2025-07-25 09:30:44,158 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-25 09:30:44,158 - INFO - TMT格式不需要sum分頁
2025-07-25 09:30:44,159 - INFO - TMT工作簿創建完成：僅Data11工作表 911行，無sum分頁
2025-07-25 09:30:44,159 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-25 09:30:44,159 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-25 09:30:44,160 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-25 09:30:44,160 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-25 09:30:44,160 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-25 09:30:44,161 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-25 09:30:44,161 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-25 09:30:44,161 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-25 09:30:44,161 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-25 09:30:44,162 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-25 09:30:44,162 - INFO - 🔍 開始統一數據收集...
2025-07-25 09:30:44,162 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-25 09:30:44,166 - INFO - 收集項目數據: 108個項目
2025-07-25 09:30:44,166 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-25 09:30:47,696 - INFO - 收集設備數據: 899個設備（已排除無測試數據的行）
2025-07-25 09:30:47,697 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-25 09:30:47,701 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-25 09:30:47,702 - INFO - 收集Site數據: 2個Site，899筆Site資料
2025-07-25 09:30:47,703 - INFO - 🔄 6.1.4 收集限制值...
2025-07-25 09:30:47,703 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-25 09:30:47,703 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-25 09:30:47,704 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-25 09:30:47,704 - INFO - ⏱️ 統一數據收集 執行時間: 3.54s
2025-07-25 09:30:47,704 - INFO - ✅ 統一數據收集完成: 項目108個, 設備899個, Site2個
2025-07-25 09:30:47,705 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 3.54s
2025-07-25 09:30:47,705 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-25 09:30:47,705 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-25 09:30:47,706 - INFO -   - 填充缺失的項目編號和名稱
2025-07-25 09:30:47,706 - INFO -   - 設置紅色字體標記
2025-07-25 09:30:47,707 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 1.8ms
2025-07-25 09:30:47,707 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-25 09:30:47,707 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-25 09:30:47,708 - INFO -   - 填充預設限制值
2025-07-25 09:30:47,708 - INFO -   - 設置紅色字體標記
2025-07-25 09:30:47,709 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 1.7ms
2025-07-25 09:30:47,709 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-25 09:30:47,709 - INFO -   - 驗證數據完整性
2025-07-25 09:30:47,710 - INFO -   - 準備統一數據摘要
2025-07-25 09:30:47,710 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-25 09:30:47,710 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-25 09:30:47,711 - INFO - 開始重新計算設備Bin值...
2025-07-25 09:30:47,822 - INFO - 重新計算並更新了899個設備的Bin值
2025-07-25 09:30:47,822 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 112.1ms
2025-07-25 09:30:47,823 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 3.66s
2025-07-25 09:30:47,823 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-25 09:30:47,823 - INFO - 應用Device2BinControl處理...
2025-07-25 09:30:47,824 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:30:47,824 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:30:47,824 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:30:47,825 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-25 09:30:47,825 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:30:47,825 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-25 09:30:47,826 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:30:47,838 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:30:47,838 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 12.9ms
2025-07-25 09:30:47,839 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:30:47,839 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-25 09:30:47,839 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-25 09:30:47,840 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:47,840 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:47,840 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:47,841 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:47,841 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:47,841 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:30:47,841 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-25 09:30:47,842 - INFO - 收集原始Bin值: 899個設備
2025-07-25 09:30:48,062 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-25 09:30:48,063 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-25 09:30:48,063 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-25 09:30:48,064 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:30:48,064 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 224.2ms
2025-07-25 09:30:48,064 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:30:48,065 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:48,065 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:48,065 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:48,066 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:48,066 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:48,066 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:48,964 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-25 09:30:48,964 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-25 09:30:48,965 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 900.2ms
2025-07-25 09:30:48,965 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 1.14s
2025-07-25 09:30:48,965 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:30:48,966 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-25 09:30:48,966 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:48,967 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:48,967 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:48,968 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:48,968 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:48,968 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-25 09:30:48,969 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:48,969 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:48,970 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:48,970 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:48,971 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:48,971 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:48,971 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:30:48,972 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:30:48,972 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:30:48,973 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-25 09:30:48,973 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:30:48,973 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-25 09:30:48,973 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:30:48,985 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:30:48,986 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 12.1ms
2025-07-25 09:30:48,986 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:30:48,987 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-25 09:30:48,987 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.1ms
2025-07-25 09:30:48,987 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:48,988 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:48,988 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:48,988 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:48,989 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:48,989 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:30:48,989 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-25 09:30:48,990 - INFO - 收集原始Bin值: 899個設備
2025-07-25 09:30:49,179 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-25 09:30:49,179 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-25 09:30:49,180 - INFO - VBA 457-469行：填充基本統計 Total=899, Pass=827, Fail=72, Yield=91.99%
2025-07-25 09:30:49,181 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-25 09:30:49,181 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-25 09:30:49,182 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=899
2025-07-25 09:30:49,183 - INFO - 創建Site統計完成: 2個Site
2025-07-25 09:30:49,183 - INFO -   Site 1: 439個設備
2025-07-25 09:30:49,183 - INFO -   Site 2: 460個設備
2025-07-25 09:30:49,184 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-25 09:30:49,184 - INFO - 填充Site 1統計完成: 439個設備
2025-07-25 09:30:49,184 - INFO - 填充Site 2統計完成: 460個設備
2025-07-25 09:30:49,185 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-25 09:30:49,189 - INFO - VBA 362-420行（修正版）：填充了6個測試項目的Bin數據，無重複
2025-07-25 09:30:49,189 - INFO - 設置 AutoFilter 範圍: A6:I12
2025-07-25 09:30:49,190 - INFO - 按 B 欄由大到小排序了 6 行資料
2025-07-25 09:30:49,191 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-25 09:30:49,191 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-25 09:30:49,191 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:30:49,192 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 204.3ms
2025-07-25 09:30:49,192 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:30:49,192 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:49,193 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:49,193 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:49,193 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:49,193 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:49,193 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:50,838 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-25 09:30:50,839 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-25 09:30:50,839 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 1.65s
2025-07-25 09:30:50,839 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 1.87s
2025-07-25 09:30:50,840 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:30:50,840 - INFO - === 統一處理管道完成 ===
2025-07-25 09:30:54,496 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-25 09:30:55,632 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx，耗時1135.6ms
2025-07-25 09:30:55,632 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx
2025-07-25 09:30:55,634 - INFO - 開始完整轉換...
2025-07-25 09:30:55,634 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-25 09:30:55,671 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-25 09:30:55,673 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-25 09:30:55,674 - INFO - === 開始7步驟統一處理管道 ===
2025-07-25 09:30:55,674 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-25 09:30:55,675 - INFO - 執行CTA到TMT格式轉換...
2025-07-25 09:30:56,069 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時391.0ms
2025-07-25 09:30:56,074 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時3.6ms
2025-07-25 09:30:56,075 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時0.7ms
2025-07-25 09:30:56,076 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時398.4ms
2025-07-25 09:30:56,076 - INFO - 應用CTA8280轉換處理...
2025-07-25 09:30:56,076 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-25 09:30:56,077 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-25 09:30:56,077 - INFO - 找到標題行在第2行
2025-07-25 09:30:56,077 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-25 09:30:56,078 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-25 09:30:56,078 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-25 09:30:56,078 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-25 09:30:56,079 - INFO - myFindedRowN=2, myColumnN=14
2025-07-25 09:30:56,088 - INFO - ⚡ 超級批量插入6行完成，耗時1.4ms
2025-07-25 09:30:56,089 - INFO - 插入了6行在最前面
2025-07-25 09:30:56,089 - INFO - ⚡ 標題行插入優化完成，耗時1.8ms
2025-07-25 09:30:56,090 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 11.0ms
2025-07-25 09:30:56,090 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-25 09:30:56,090 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-25 09:30:56,091 - INFO - 清空位置 A8: "Index_No"
2025-07-25 09:30:56,091 - INFO - 清空位置 B8: "Dut_No"
2025-07-25 09:30:56,091 - INFO - 清空位置 A10: "ASD"
2025-07-25 09:30:56,091 - INFO - 清空位置 B10: "QQ"
2025-07-25 09:30:56,092 - INFO - 清空位置 A11: "A"
2025-07-25 09:30:56,092 - INFO - 清空位置 B11: "B"
2025-07-25 09:30:56,092 - INFO - ✅ 清空了6個多餘資料位置
2025-07-25 09:30:56,093 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-25 09:30:56,093 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 3.6ms
2025-07-25 09:30:56,094 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-25 09:30:56,096 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 2.0ms
2025-07-25 09:30:56,096 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-25 09:30:56,097 - INFO - 找到SW_Bin列在第6列
2025-07-25 09:30:56,097 - INFO - 處理了17個設備的數據
2025-07-25 09:30:56,098 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 1.4ms
2025-07-25 09:30:56,098 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 21.8ms
2025-07-25 09:30:56,098 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-25 09:30:56,099 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-25 09:30:56,099 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-25 09:30:56,099 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-25 09:30:56,100 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-25 09:30:56,100 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-25 09:30:56,100 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-25 09:30:56,101 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-25 09:30:56,101 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-25 09:30:56,101 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-25 09:30:56,102 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-25 09:30:56,102 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-25 09:30:56,102 - INFO - 🔍 開始統一數據收集...
2025-07-25 09:30:56,103 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-25 09:30:56,103 - INFO - 收集項目數據: 58個項目
2025-07-25 09:30:56,104 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-25 09:30:56,105 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-25 09:30:56,106 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-25 09:30:56,106 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-25 09:30:56,106 - INFO - 收集Site數據: 4個Site，16筆Site資料
2025-07-25 09:30:56,107 - INFO - 🔄 6.1.4 收集限制值...
2025-07-25 09:30:56,107 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-25 09:30:56,107 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-25 09:30:56,108 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-25 09:30:56,108 - INFO - ⏱️ 統一數據收集 執行時間: 5.5ms
2025-07-25 09:30:56,108 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-25 09:30:56,109 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 6.7ms
2025-07-25 09:30:56,109 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-25 09:30:56,110 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-25 09:30:56,110 - INFO -   - 填充缺失的項目編號和名稱
2025-07-25 09:30:56,110 - INFO -   - 設置紅色字體標記
2025-07-25 09:30:56,112 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 2.5ms
2025-07-25 09:30:56,112 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-25 09:30:56,112 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-25 09:30:56,113 - INFO -   - 填充預設限制值
2025-07-25 09:30:56,113 - INFO -   - 設置紅色字體標記
2025-07-25 09:30:56,115 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 3.2ms
2025-07-25 09:30:56,116 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-25 09:30:56,116 - INFO -   - 驗證數據完整性
2025-07-25 09:30:56,116 - INFO -   - 準備統一數據摘要
2025-07-25 09:30:56,117 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-25 09:30:56,117 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-25 09:30:56,117 - INFO - 開始重新計算設備Bin值...
2025-07-25 09:30:56,119 - INFO - 重新計算並更新了17個設備的Bin值
2025-07-25 09:30:56,119 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 2.3ms
2025-07-25 09:30:56,120 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 17.9ms
2025-07-25 09:30:56,120 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-25 09:30:56,120 - INFO - 應用Device2BinControl處理...
2025-07-25 09:30:56,121 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:30:56,121 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:30:56,121 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:30:56,122 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-25 09:30:56,122 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:30:56,122 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-25 09:30:56,123 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:30:56,123 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:30:56,124 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: <1ms
2025-07-25 09:30:56,124 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:30:56,124 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-25 09:30:56,125 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-25 09:30:56,125 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:56,125 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:56,126 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:56,126 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:56,126 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:56,127 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:30:56,127 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-25 09:30:56,127 - INFO - 收集原始Bin值: 17個設備
2025-07-25 09:30:56,131 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-25 09:30:56,132 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-25 09:30:56,132 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-25 09:30:56,132 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:30:56,133 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 7.9ms
2025-07-25 09:30:56,133 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:30:56,134 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:56,134 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:56,134 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:56,134 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:56,135 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:56,135 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:56,154 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-25 09:30:56,154 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-25 09:30:56,155 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 21.4ms
2025-07-25 09:30:56,155 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 34.3ms
2025-07-25 09:30:56,155 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:30:56,156 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-25 09:30:56,156 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:56,156 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:56,157 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:56,157 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:56,157 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:56,158 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-25 09:30:56,158 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:56,158 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:56,158 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:56,159 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:56,159 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:56,159 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:56,160 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-25 09:30:56,160 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-25 09:30:56,160 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-25 09:30:56,161 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-25 09:30:56,161 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-25 09:30:56,161 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-25 09:30:56,162 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-25 09:30:56,162 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-25 09:30:56,163 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 1.1ms
2025-07-25 09:30:56,163 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-25 09:30:56,163 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-25 09:30:56,164 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-25 09:30:56,164 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-25 09:30:56,165 - INFO -   - 7.1.1 收集原始Bin值
2025-07-25 09:30:56,165 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-25 09:30:56,165 - INFO -   - 7.1.3 計算Bin統計
2025-07-25 09:30:56,166 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-25 09:30:56,166 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-25 09:30:56,166 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-25 09:30:56,167 - INFO - 收集原始Bin值: 17個設備
2025-07-25 09:30:56,170 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-25 09:30:56,170 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-25 09:30:56,171 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-25 09:30:56,172 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-25 09:30:56,172 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-25 09:30:56,173 - INFO - Site信息檢查: total_site_no=4, good_site_n=True, site_data數量=16
2025-07-25 09:30:56,173 - INFO - 創建Site統計完成: 4個Site
2025-07-25 09:30:56,173 - INFO -   Site 1: 4個設備
2025-07-25 09:30:56,173 - INFO -   Site 2: 4個設備
2025-07-25 09:30:56,174 - INFO -   Site 3: 4個設備
2025-07-25 09:30:56,174 - INFO -   Site 4: 4個設備
2025-07-25 09:30:56,174 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-25 09:30:56,175 - INFO - 填充Site 1統計完成: 4個設備
2025-07-25 09:30:56,175 - INFO - 填充Site 2統計完成: 4個設備
2025-07-25 09:30:56,175 - INFO - 填充Site 3統計完成: 4個設備
2025-07-25 09:30:56,176 - INFO - 填充Site 4統計完成: 4個設備
2025-07-25 09:30:56,176 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-25 09:30:56,177 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-25 09:30:56,177 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-25 09:30:56,177 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-25 09:30:56,178 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-25 09:30:56,178 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-25 09:30:56,178 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-25 09:30:56,179 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 14.4ms
2025-07-25 09:30:56,179 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-25 09:30:56,179 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-25 09:30:56,180 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-25 09:30:56,180 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-25 09:30:56,180 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-25 09:30:56,181 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-25 09:30:56,181 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-25 09:30:56,211 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-25 09:30:56,212 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-25 09:30:56,212 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 33.2ms
2025-07-25 09:30:56,213 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 52.8ms
2025-07-25 09:30:56,213 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-25 09:30:56,213 - INFO - === 統一處理管道完成 ===
2025-07-25 09:30:57,460 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-25 09:30:57,961 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx，耗時500.5ms
2025-07-25 09:30:57,961 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx
2025-07-25 09:32:35,388 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_oii33k2k
2025-07-25 09:32:35,390 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_p8b7w617
2025-07-28 21:07:08,640 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-28 21:07:08,642 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_ssetntwn
2025-07-28 21:07:08,643 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-28 21:07:08,646 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_ssetntwn\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 21:07:08,648 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd.zip
2025-07-28 21:07:08,648 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_8wap4un2
2025-07-28 21:07:08,648 - INFO - ZIP檔案包含: ['G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd']
2025-07-28 21:07:08,671 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_8wap4un2\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-28 21:07:09,361 - INFO - 開始完整轉換...
2025-07-28 21:07:09,362 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_ssetntwn\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 21:07:09,442 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-28 21:07:09,445 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-28 21:07:09,445 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:07:09,446 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-28 21:07:09,446 - INFO - 執行CTA到TMT格式轉換...
2025-07-28 21:07:14,993 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時5538.7ms
2025-07-28 21:07:16,716 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時1720.7ms
2025-07-28 21:07:16,752 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時34.5ms
2025-07-28 21:07:16,752 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時7301.9ms
2025-07-28 21:07:16,752 - INFO - 應用CTA8280轉換處理...
2025-07-28 21:07:16,752 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-28 21:07:16,753 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-28 21:07:16,810 - INFO - 找到標題行在第2行
2025-07-28 21:07:16,810 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 56.9ms
2025-07-28 21:07:16,810 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-28 21:07:16,810 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-28 21:07:16,811 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-28 21:07:16,926 - INFO - myFindedRowN=2, myColumnN=14
2025-07-28 21:07:22,684 - INFO - ⚡ 超級批量插入6行完成，耗時867.9ms
2025-07-28 21:07:22,684 - INFO - 插入了6行在最前面
2025-07-28 21:07:22,685 - INFO - ⚡ 標題行插入優化完成，耗時867.9ms
2025-07-28 21:07:22,685 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 5.87s
2025-07-28 21:07:22,685 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-28 21:07:22,686 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-28 21:07:22,686 - INFO - 清空位置 A8: "Index_No"
2025-07-28 21:07:22,686 - INFO - 清空位置 B8: "Dut_No"
2025-07-28 21:07:22,686 - INFO - 清空位置 A10: "Max"
2025-07-28 21:07:22,686 - INFO - 清空位置 B10: "Max"
2025-07-28 21:07:22,687 - INFO - 清空位置 A11: "Min"
2025-07-28 21:07:22,687 - INFO - 清空位置 B11: "Min"
2025-07-28 21:07:22,687 - INFO - ✅ 清空了6個多餘資料位置
2025-07-28 21:07:22,923 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 237.6ms
2025-07-28 21:07:22,923 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-28 21:07:22,930 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 6.3ms
2025-07-28 21:07:22,930 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-28 21:07:22,990 - INFO - 找到SW_Bin列在第6列
2025-07-28 21:07:23,051 - INFO - 處理了264個設備的數據
2025-07-28 21:07:23,051 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 121.7ms
2025-07-28 21:07:23,052 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 6.30s
2025-07-28 21:07:23,052 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-28 21:07:23,052 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-28 21:07:23,052 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:07:23,053 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:07:23,053 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:07:23,053 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:07:23,053 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:07:23,053 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:07:23,054 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:07:23,054 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:07:23,054 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:07:23,054 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:07:23,054 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:07:23,054 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:07:23,114 - INFO - 收集項目數據: 197個項目
2025-07-28 21:07:23,114 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:07:40,380 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-28 21:07:40,380 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 21:07:40,456 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-28 21:07:40,457 - INFO - 收集Site數據: 1個Site，264筆Site資料
2025-07-28 21:07:40,457 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 21:07:40,458 - INFO - 收集限制值: Max 184個, Min 184個
2025-07-28 21:07:40,458 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 21:07:40,461 - INFO - 設置第6行項目編號: 從第3列開始，共197個項目
2025-07-28 21:07:40,461 - INFO - ⏱️ 統一數據收集 執行時間: 17.41s
2025-07-28 21:07:40,462 - INFO - ✅ 統一數據收集完成: 項目197個, 設備264個, Site1個
2025-07-28 21:07:40,462 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 17.41s
2025-07-28 21:07:40,462 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-28 21:07:40,462 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-28 21:07:40,463 - INFO -   - 填充缺失的項目編號和名稱
2025-07-28 21:07:40,463 - INFO -   - 設置紅色字體標記
2025-07-28 21:07:40,505 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 42.9ms
2025-07-28 21:07:40,505 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-28 21:07:40,505 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-28 21:07:40,506 - INFO -   - 填充預設限制值
2025-07-28 21:07:40,506 - INFO -   - 設置紅色字體標記
2025-07-28 21:07:40,528 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 23.3ms
2025-07-28 21:07:40,528 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-28 21:07:40,530 - INFO -   - 驗證數據完整性
2025-07-28 21:07:40,530 - INFO -   - 準備統一數據摘要
2025-07-28 21:07:40,530 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-28 21:07:40,531 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-28 21:07:40,531 - INFO - 開始重新計算設備Bin值...
2025-07-28 21:07:40,733 - INFO - 重新計算並更新了264個設備的Bin值
2025-07-28 21:07:40,733 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 202.4ms
2025-07-28 21:07:40,733 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 17.68s
2025-07-28 21:07:40,733 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-28 21:07:40,735 - INFO - 應用Device2BinControl處理...
2025-07-28 21:07:40,735 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:07:40,735 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:07:40,736 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:07:40,736 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:07:40,736 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:07:40,736 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:07:40,737 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:07:40,965 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:07:40,966 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 229.5ms
2025-07-28 21:07:40,966 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:07:40,966 - INFO - ✅ 數據摘要: 項目197個, 設備264個, Site1個
2025-07-28 21:07:40,967 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:07:40,967 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:07:40,967 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:07:40,967 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:07:40,968 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:07:40,968 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:07:40,968 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:07:40,968 - INFO - 使用統一數據: 設備264個, 項目197個
2025-07-28 21:07:40,970 - INFO - 收集原始Bin值: 264個設備
2025-07-28 21:07:41,630 - INFO - 應用染色邏輯: 63個設備的失敗項目
2025-07-28 21:07:41,632 - INFO - Bin統計: Pass=201, Fail=63, Total=264
2025-07-28 21:07:41,632 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-28 21:07:41,632 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:07:41,633 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 665.6ms
2025-07-28 21:07:41,633 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:07:41,633 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:07:41,633 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:07:41,634 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:07:41,634 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:07:41,634 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:07:41,634 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:07:44,352 - INFO - ⚡ 超級批量字體設置完成，總共處理52126個cell
2025-07-28 21:07:44,353 - INFO - 設置數據區域字體顏色: 行13-276, 列2-199
2025-07-28 21:07:44,353 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 2.72s
2025-07-28 21:07:44,353 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 3.62s
2025-07-28 21:07:44,353 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:07:44,354 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-28 21:07:44,354 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-28 21:07:44,354 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:07:44,355 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:07:44,355 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:07:44,355 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:07:44,355 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-28 21:07:44,356 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:07:44,356 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:07:44,356 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:07:44,357 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:07:44,357 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:07:44,357 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:07:44,357 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:07:44,358 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:07:44,358 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:07:44,358 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:07:44,358 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:07:44,358 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:07:44,359 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:07:44,557 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:07:44,557 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 197.7ms
2025-07-28 21:07:44,558 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:07:44,558 - INFO - ✅ 數據摘要: 項目197個, 設備264個, Site1個
2025-07-28 21:07:44,558 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:07:44,558 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:07:44,558 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:07:44,559 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:07:44,559 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:07:44,560 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:07:44,560 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:07:44,560 - INFO - 使用統一數據: 設備264個, 項目197個
2025-07-28 21:07:44,561 - INFO - 收集原始Bin值: 264個設備
2025-07-28 21:07:45,155 - INFO - 應用染色邏輯: 63個設備的失敗項目
2025-07-28 21:07:45,155 - INFO - Bin統計: Pass=201, Fail=63, Total=264
2025-07-28 21:07:45,158 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=201, Fail=63, Yield=76.14%
2025-07-28 21:07:45,159 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_ssetntwn\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 21:07:45,160 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-28 21:07:45,161 - INFO - Site信息檢查: total_site_no=1, good_site_n=True, site_data數量=264
2025-07-28 21:07:45,161 - INFO - 創建Site統計完成: 1個Site
2025-07-28 21:07:45,162 - INFO -   Site 1: 264個設備
2025-07-28 21:07:45,162 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-28 21:07:45,162 - INFO - 填充Site 1統計完成: 264個設備
2025-07-28 21:07:45,162 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-28 21:07:45,234 - INFO - VBA 362-420行（修正版）：填充了16個測試項目的Bin數據，無重複
2025-07-28 21:07:45,235 - INFO - 設置 AutoFilter 範圍: A6:G22
2025-07-28 21:07:45,237 - INFO - 按 B 欄由大到小排序了 16 行資料
2025-07-28 21:07:45,238 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-28 21:07:45,238 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 21:07:45,238 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:07:45,238 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 680.1ms
2025-07-28 21:07:45,240 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:07:45,240 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:07:45,240 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:07:45,240 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:07:45,241 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:07:45,241 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:07:45,241 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:07:49,246 - INFO - ⚡ 超級批量字體設置完成，總共處理52126個cell
2025-07-28 21:07:49,247 - INFO - 設置數據區域字體顏色: 行13-276, 列2-199
2025-07-28 21:07:49,247 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 4.01s
2025-07-28 21:07:49,247 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 4.89s
2025-07-28 21:07:49,247 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:07:49,248 - INFO - === 統一處理管道完成 ===
2025-07-28 21:08:12,124 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-28 21:08:17,973 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時5848.1ms
2025-07-28 21:08:17,973 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-28 21:08:17,976 - INFO - 開始完整轉換...
2025-07-28 21:08:17,976 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_8wap4un2\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-28 21:08:18,168 - INFO - 成功讀取CSV文件，大小: (7971, 110)
2025-07-28 21:08:18,168 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-28 21:08:18,168 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:08:18,170 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-28 21:08:18,170 - INFO - 創建TMT格式工作簿...
2025-07-28 21:08:26,980 - INFO - ⚡ 優化寫入完成: 7971行 × 110列，耗時8807.7ms
2025-07-28 21:08:26,984 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-28 21:08:26,984 - INFO - TMT格式不需要sum分頁
2025-07-28 21:08:26,985 - INFO - TMT工作簿創建完成：僅Data11工作表 7971行，無sum分頁
2025-07-28 21:08:26,985 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:08:26,985 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:08:26,986 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:08:26,986 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:08:26,986 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:08:26,986 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:08:26,987 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:08:26,987 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:08:26,987 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:08:26,987 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:08:26,987 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:08:26,988 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:08:27,322 - INFO - 收集項目數據: 108個項目
2025-07-28 21:08:27,322 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:24:47,546 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_ssetntwn
2025-07-28 21:24:47,670 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_8wap4un2
2025-07-28 21:25:40,281 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:25:40,281 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:25:40,281 - INFO - 收集項目數據: 2個項目
2025-07-28 21:25:40,281 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:25:40,281 - INFO - 收集設備數據: 5個設備（已排除無測試數據的行）
2025-07-28 21:25:40,281 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 21:25:40,281 - INFO - 找到Site列: 第12行第4列，標題: Site
2025-07-28 21:25:40,281 - INFO - 收集Site數據: 最大Site號=5，實際Site數=3，5筆Site資料
2025-07-28 21:25:40,283 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 21:25:40,283 - INFO - 收集限制值: Max 0個, Min 0個
2025-07-28 21:25:40,283 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 21:25:40,283 - INFO - 設置第6行項目編號: 從第3列開始，共2個項目
2025-07-28 21:25:40,283 - INFO - ⏱️ 統一數據收集 執行時間: 1.4ms
2025-07-28 21:25:40,283 - INFO - ✅ 統一數據收集完成: 項目2個, 設備5個, Site5個
2025-07-28 21:25:40,284 - INFO - 創建Site統計完成: 最大Site號=5，實際有設備的Site數=3
2025-07-28 21:25:40,284 - INFO -   Site 1: 2個設備
2025-07-28 21:25:40,284 - INFO -   Site 2: 0個設備（空Site，但會創建統計欄位）
2025-07-28 21:25:40,284 - INFO -   Site 3: 2個設備
2025-07-28 21:25:40,285 - INFO -   Site 4: 0個設備（空Site，但會創建統計欄位）
2025-07-28 21:25:40,285 - INFO -   Site 5: 1個設備
2025-07-28 21:26:15,317 - INFO - 開始完整轉換...
2025-07-28 21:26:15,318 - INFO - 讀取CSV文件: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/GMT_G2304.csv
2025-07-28 21:26:15,343 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-28 21:26:15,351 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-28 21:26:15,352 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:26:15,352 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-28 21:26:15,352 - INFO - 執行CTA到TMT格式轉換...
2025-07-28 21:26:17,075 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時1711.4ms
2025-07-28 21:26:17,089 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時13.4ms
2025-07-28 21:26:17,093 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時3.0ms
2025-07-28 21:26:17,093 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時1733.9ms
2025-07-28 21:26:17,093 - INFO - 應用CTA8280轉換處理...
2025-07-28 21:26:17,093 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-28 21:26:17,093 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-28 21:26:17,094 - INFO - 找到標題行在第2行
2025-07-28 21:26:17,094 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 1.0ms
2025-07-28 21:26:17,094 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-28 21:26:17,094 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-28 21:26:17,095 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-28 21:26:17,096 - INFO - myFindedRowN=2, myColumnN=14
2025-07-28 21:26:17,138 - INFO - ⚡ 超級批量插入6行完成，耗時6.1ms
2025-07-28 21:26:17,139 - INFO - 插入了6行在最前面
2025-07-28 21:26:17,139 - INFO - ⚡ 標題行插入優化完成，耗時7.1ms
2025-07-28 21:26:17,139 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 44.2ms
2025-07-28 21:26:17,139 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-28 21:26:17,141 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-28 21:26:17,141 - INFO - 清空位置 A8: "Index_No"
2025-07-28 21:26:17,141 - INFO - 清空位置 B8: "Dut_No"
2025-07-28 21:26:17,141 - INFO - 清空位置 A10: "ASD"
2025-07-28 21:26:17,141 - INFO - 清空位置 B10: "QQ"
2025-07-28 21:26:17,142 - INFO - 清空位置 A11: "A"
2025-07-28 21:26:17,142 - INFO - 清空位置 B11: "B"
2025-07-28 21:26:17,142 - INFO - ✅ 清空了6個多餘資料位置
2025-07-28 21:26:17,143 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-28 21:26:17,144 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 4.8ms
2025-07-28 21:26:17,145 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-28 21:26:17,160 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 14.8ms
2025-07-28 21:26:17,160 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-28 21:26:17,161 - INFO - 找到SW_Bin列在第6列
2025-07-28 21:26:17,162 - INFO - 處理了17個設備的數據
2025-07-28 21:26:17,162 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 2.0ms
2025-07-28 21:26:17,162 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 68.8ms
2025-07-28 21:26:17,163 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-28 21:26:17,163 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-28 21:26:17,163 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:26:17,164 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:26:17,164 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:26:17,164 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:26:17,164 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:26:17,165 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:26:17,165 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:26:17,165 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:26:17,166 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:26:17,166 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:26:17,166 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:26:17,166 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:26:17,167 - INFO - 收集項目數據: 58個項目
2025-07-28 21:26:17,168 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:26:17,178 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-28 21:26:17,178 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 21:26:17,179 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-28 21:26:17,180 - INFO - 收集Site數據: 最大Site號=4，實際Site數=4，16筆Site資料
2025-07-28 21:26:17,180 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 21:26:17,181 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-28 21:26:17,181 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 21:26:17,181 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-28 21:26:17,182 - INFO - ⏱️ 統一數據收集 執行時間: 15.4ms
2025-07-28 21:26:17,182 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-28 21:26:17,182 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 15.4ms
2025-07-28 21:26:17,182 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-28 21:26:17,183 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-28 21:26:17,183 - INFO -   - 填充缺失的項目編號和名稱
2025-07-28 21:26:17,183 - INFO -   - 設置紅色字體標記
2025-07-28 21:26:17,186 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 4.0ms
2025-07-28 21:26:17,186 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-28 21:26:17,186 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-28 21:26:17,186 - INFO -   - 填充預設限制值
2025-07-28 21:26:17,187 - INFO -   - 設置紅色字體標記
2025-07-28 21:26:17,192 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 6.3ms
2025-07-28 21:26:17,192 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-28 21:26:17,192 - INFO -   - 驗證數據完整性
2025-07-28 21:26:17,193 - INFO -   - 準備統一數據摘要
2025-07-28 21:26:17,193 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-28 21:26:17,193 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-28 21:26:17,193 - INFO - 開始重新計算設備Bin值...
2025-07-28 21:26:17,195 - INFO - 重新計算並更新了17個設備的Bin值
2025-07-28 21:26:17,196 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 3.0ms
2025-07-28 21:26:17,196 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 29.7ms
2025-07-28 21:26:17,196 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-28 21:26:17,196 - INFO - 應用Device2BinControl處理...
2025-07-28 21:26:17,196 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:26:17,196 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:26:17,196 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:26:17,197 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:26:17,198 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.5ms
2025-07-28 21:26:17,198 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:26:17,198 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:26:17,200 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:26:17,200 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.6ms
2025-07-28 21:26:17,201 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:26:17,201 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-28 21:26:17,201 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:26:17,201 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:26:17,202 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:26:17,202 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:26:17,202 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:26:17,202 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:26:17,202 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:26:17,202 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-28 21:26:17,203 - INFO - 收集原始Bin值: 17個設備
2025-07-28 21:26:17,213 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-28 21:26:17,213 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-28 21:26:17,214 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-28 21:26:17,214 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:26:17,215 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 12.6ms
2025-07-28 21:26:17,215 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:26:17,215 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:26:17,215 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:26:17,215 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:26:17,216 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:26:17,216 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:26:17,216 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:26:17,273 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-28 21:26:17,274 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-28 21:26:17,274 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 58.8ms
2025-07-28 21:26:17,274 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 78.0ms
2025-07-28 21:26:17,274 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:26:17,274 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-28 21:26:17,275 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-28 21:26:17,275 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:26:17,275 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:26:17,275 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:26:17,275 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:26:17,275 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-28 21:26:17,275 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:26:17,276 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:26:17,276 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:26:17,276 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:26:17,276 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:26:17,276 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:26:17,277 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:26:17,277 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:26:17,277 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:26:17,278 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:26:17,278 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:26:17,278 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:26:17,278 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:26:17,280 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:26:17,280 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.2ms
2025-07-28 21:26:17,281 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:26:17,281 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-28 21:26:17,281 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-28 21:26:17,281 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:26:17,281 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:26:17,281 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:26:17,281 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:26:17,281 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:26:17,282 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:26:17,283 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-28 21:26:17,283 - INFO - 收集原始Bin值: 17個設備
2025-07-28 21:26:17,294 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-28 21:26:17,294 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-28 21:26:17,296 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-28 21:26:17,297 - INFO - 添加原始檔案超連結: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/GMT_G2304.csv
2025-07-28 21:26:17,298 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-28 21:26:17,298 - INFO - Site信息檢查: total_site_no=4, good_site_n=True, site_data數量=16
2025-07-28 21:26:17,299 - INFO - 創建Site統計完成: 最大Site號=4，實際有設備的Site數=4
2025-07-28 21:26:17,299 - INFO -   Site 1: 4個設備
2025-07-28 21:26:17,299 - INFO -   Site 2: 4個設備
2025-07-28 21:26:17,300 - INFO -   Site 3: 4個設備
2025-07-28 21:26:17,300 - INFO -   Site 4: 4個設備
2025-07-28 21:26:17,300 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-28 21:26:17,300 - INFO - 填充Site 1統計完成: 4個設備
2025-07-28 21:26:17,300 - INFO - 填充Site 2統計完成: 4個設備
2025-07-28 21:26:17,302 - INFO - 填充Site 3統計完成: 4個設備
2025-07-28 21:26:17,302 - INFO - 填充Site 4統計完成: 4個設備
2025-07-28 21:26:17,302 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-28 21:26:17,303 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-28 21:26:17,304 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-28 21:26:17,304 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-28 21:26:17,305 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-28 21:26:17,305 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 21:26:17,305 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:26:17,305 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 24.1ms
2025-07-28 21:26:17,307 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:26:17,307 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:26:17,307 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:26:17,307 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:26:17,307 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:26:17,308 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:26:17,308 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:26:17,388 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-28 21:26:17,388 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-28 21:26:17,388 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 81.7ms
2025-07-28 21:26:17,389 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 111.8ms
2025-07-28 21:26:17,389 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:26:17,389 - INFO - === 統一處理管道完成 ===
2025-07-28 21:26:22,062 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-28 21:26:23,429 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\GMT_G2304_clean_converted.xlsx，耗時1366.5ms
2025-07-28 21:26:23,429 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\GMT_G2304_clean_converted.xlsx
2025-07-28 21:27:09,325 - INFO - 開始完整轉換...
2025-07-28 21:27:09,326 - INFO - 讀取CSV文件: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G5440WC(CC)_GHBR04.1_02.spd
2025-07-28 21:27:09,359 - INFO - 成功讀取CSV文件，大小: (911, 110)
2025-07-28 21:27:09,360 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-28 21:27:09,360 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:27:09,360 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-28 21:27:09,360 - INFO - 創建TMT格式工作簿...
2025-07-28 21:27:10,285 - INFO - ⚡ 優化寫入完成: 911行 × 110列，耗時920.7ms
2025-07-28 21:27:10,286 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-28 21:27:10,286 - INFO - TMT格式不需要sum分頁
2025-07-28 21:27:10,286 - INFO - TMT工作簿創建完成：僅Data11工作表 911行，無sum分頁
2025-07-28 21:27:10,286 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:27:10,287 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:27:10,287 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:27:10,287 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:27:10,287 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:27:10,287 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:27:10,288 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:27:10,288 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:27:10,288 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:27:10,288 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:27:10,288 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:27:10,288 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:27:10,323 - INFO - 收集項目數據: 108個項目
2025-07-28 21:27:10,323 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:27:42,700 - INFO - 收集設備數據: 899個設備（已排除無測試數據的行）
2025-07-28 21:27:42,700 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 21:27:42,746 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-28 21:27:42,750 - INFO - 收集Site數據: 最大Site號=2，實際Site數=2，899筆Site資料
2025-07-28 21:27:42,750 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 21:27:42,751 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-28 21:27:42,752 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 21:27:42,752 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-28 21:27:42,753 - INFO - ⏱️ 統一數據收集 執行時間: 32.46s
2025-07-28 21:27:42,753 - INFO - ✅ 統一數據收集完成: 項目108個, 設備899個, Site2個
2025-07-28 21:27:42,753 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 32.46s
2025-07-28 21:27:42,753 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-28 21:27:42,754 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-28 21:27:42,754 - INFO -   - 填充缺失的項目編號和名稱
2025-07-28 21:27:42,754 - INFO -   - 設置紅色字體標記
2025-07-28 21:27:42,758 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 4.4ms
2025-07-28 21:27:42,758 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-28 21:27:42,759 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-28 21:27:42,759 - INFO -   - 填充預設限制值
2025-07-28 21:27:42,759 - INFO -   - 設置紅色字體標記
2025-07-28 21:27:42,760 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 2.0ms
2025-07-28 21:27:42,761 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-28 21:27:42,761 - INFO -   - 驗證數據完整性
2025-07-28 21:27:42,761 - INFO -   - 準備統一數據摘要
2025-07-28 21:27:42,762 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-28 21:27:42,762 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-28 21:27:42,762 - INFO - 開始重新計算設備Bin值...
2025-07-28 21:27:43,068 - INFO - 重新計算並更新了899個設備的Bin值
2025-07-28 21:27:43,069 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 306.5ms
2025-07-28 21:27:43,069 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 32.78s
2025-07-28 21:27:43,069 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-28 21:27:43,070 - INFO - 應用Device2BinControl處理...
2025-07-28 21:27:43,070 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:27:43,070 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:27:43,070 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:27:43,071 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-28 21:27:43,071 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:27:43,071 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-28 21:27:43,071 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:27:43,178 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:27:43,179 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 107.6ms
2025-07-28 21:27:43,179 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:27:43,179 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-28 21:27:43,180 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.3ms
2025-07-28 21:27:43,180 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:27:43,180 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:27:43,180 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:27:43,181 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:27:43,181 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:27:43,181 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:27:43,182 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-28 21:27:43,183 - INFO - 收集原始Bin值: 899個設備
2025-07-28 21:27:44,090 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-28 21:27:44,092 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-28 21:27:44,093 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-28 21:27:44,093 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:27:44,093 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 913.4ms
2025-07-28 21:27:44,093 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:27:44,094 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:27:44,094 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:27:44,094 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:27:44,095 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:27:44,095 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:27:44,095 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:27:48,526 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-28 21:27:48,527 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-28 21:27:48,527 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 4.43s
2025-07-28 21:27:48,527 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 5.46s
2025-07-28 21:27:48,528 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:27:48,528 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-28 21:27:48,528 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-28 21:27:48,528 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:27:48,529 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:27:48,529 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:27:48,530 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:27:48,530 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-28 21:27:48,530 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:27:48,530 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:27:48,531 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:27:48,531 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:27:48,531 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:27:48,531 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:27:48,532 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:27:48,532 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:27:48,532 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:27:48,532 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-28 21:27:48,533 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.0ms
2025-07-28 21:27:48,533 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-28 21:27:48,533 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:27:48,651 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:27:48,651 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 118.1ms
2025-07-28 21:27:48,652 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:27:48,652 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-28 21:27:48,652 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:27:48,652 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:27:48,653 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:27:48,653 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:27:48,653 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:27:48,653 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:27:48,654 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:27:48,654 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-28 21:27:48,655 - INFO - 收集原始Bin值: 899個設備
2025-07-28 21:27:49,594 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-28 21:27:49,597 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-28 21:27:49,599 - INFO - VBA 457-469行：填充基本統計 Total=899, Pass=827, Fail=72, Yield=91.99%
2025-07-28 21:27:49,601 - INFO - 添加原始檔案超連結: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G5440WC(CC)_GHBR04.1_02.spd
2025-07-28 21:27:49,605 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-28 21:27:49,606 - INFO - Site信息檢查: total_site_no=2, good_site_n=True, site_data數量=899
2025-07-28 21:27:49,606 - INFO - 創建Site統計完成: 最大Site號=2，實際有設備的Site數=2
2025-07-28 21:27:49,607 - INFO -   Site 1: 439個設備
2025-07-28 21:27:49,607 - INFO -   Site 2: 460個設備
2025-07-28 21:27:49,607 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-28 21:27:49,608 - INFO - 填充Site 1統計完成: 439個設備
2025-07-28 21:27:49,608 - INFO - 填充Site 2統計完成: 460個設備
2025-07-28 21:27:49,608 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-28 21:27:49,650 - INFO - VBA 362-420行（修正版）：填充了6個測試項目的Bin數據，無重複
2025-07-28 21:27:49,650 - INFO - 設置 AutoFilter 範圍: A6:I12
2025-07-28 21:27:49,651 - INFO - 按 B 欄由大到小排序了 6 行資料
2025-07-28 21:27:49,652 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-28 21:27:49,652 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 21:27:49,652 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:27:49,652 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1000.0ms
2025-07-28 21:27:49,653 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:27:49,653 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:27:49,653 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:27:49,653 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:27:49,654 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:27:49,654 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:27:49,654 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:27:56,767 - INFO - ⚡ 超級批量字體設置完成，總共處理97888個cell
2025-07-28 21:27:56,767 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-28 21:27:56,767 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 7.11s
2025-07-28 21:27:56,767 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 8.24s
2025-07-28 21:27:56,767 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:27:56,767 - INFO - === 統一處理管道完成 ===
2025-07-28 21:28:09,206 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-28 21:28:12,377 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx，耗時3171.8ms
2025-07-28 21:28:12,378 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx
2025-07-28 21:38:53,483 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-28 21:38:53,484 - INFO - 設備行14: 失敗項目Test1, 使用Bin值5
2025-07-28 21:38:53,484 - INFO - 設備行15: 失敗項目Test2, 使用Bin值10
2025-07-28 21:38:53,484 - INFO - 設備行16: 失敗項目Test1, 使用Bin值5
2025-07-28 21:38:53,484 - INFO - 重新計算並更新了4個設備的Bin值（使用第6行項目特定Bin值）
2025-07-28 21:39:53,493 - INFO - 開始完整轉換...
2025-07-28 21:39:53,493 - INFO - 讀取CSV文件: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/GMT_G2304.csv
2025-07-28 21:39:53,518 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-28 21:39:53,526 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-28 21:39:53,526 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:39:53,527 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-28 21:39:53,527 - INFO - 執行CTA到TMT格式轉換...
2025-07-28 21:39:55,225 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時1687.4ms
2025-07-28 21:39:55,241 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時13.4ms
2025-07-28 21:39:55,245 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時3.0ms
2025-07-28 21:39:55,245 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時1709.6ms
2025-07-28 21:39:55,246 - INFO - 應用CTA8280轉換處理...
2025-07-28 21:39:55,246 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-28 21:39:55,246 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-28 21:39:55,247 - INFO - 找到標題行在第2行
2025-07-28 21:39:55,247 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 1.0ms
2025-07-28 21:39:55,248 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-28 21:39:55,248 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-28 21:39:55,248 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-28 21:39:55,250 - INFO - myFindedRowN=2, myColumnN=14
2025-07-28 21:39:55,291 - INFO - ⚡ 超級批量插入6行完成，耗時6.0ms
2025-07-28 21:39:55,291 - INFO - 插入了6行在最前面
2025-07-28 21:39:55,293 - INFO - ⚡ 標題行插入優化完成，耗時6.0ms
2025-07-28 21:39:55,293 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 45.0ms
2025-07-28 21:39:55,293 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-28 21:39:55,294 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-28 21:39:55,294 - INFO - 清空位置 A8: "Index_No"
2025-07-28 21:39:55,294 - INFO - 清空位置 B8: "Dut_No"
2025-07-28 21:39:55,294 - INFO - 清空位置 A10: "ASD"
2025-07-28 21:39:55,294 - INFO - 清空位置 B10: "QQ"
2025-07-28 21:39:55,294 - INFO - 清空位置 A11: "A"
2025-07-28 21:39:55,294 - INFO - 清空位置 B11: "B"
2025-07-28 21:39:55,295 - INFO - ✅ 清空了6個多餘資料位置
2025-07-28 21:39:55,296 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-28 21:39:55,297 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 4.5ms
2025-07-28 21:39:55,297 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-28 21:39:55,313 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 15.9ms
2025-07-28 21:39:55,313 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-28 21:39:55,315 - INFO - 找到SW_Bin列在第6列
2025-07-28 21:39:55,315 - INFO - 處理了17個設備的數據
2025-07-28 21:39:55,315 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 2.0ms
2025-07-28 21:39:55,315 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 69.4ms
2025-07-28 21:39:55,315 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-28 21:39:55,317 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-28 21:39:55,317 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:39:55,317 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:39:55,317 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:39:55,318 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:39:55,318 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:39:55,318 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:39:55,318 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:39:55,319 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:39:55,319 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:39:55,319 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:39:55,319 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:39:55,320 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:39:55,321 - INFO - 收集項目數據: 58個項目
2025-07-28 21:39:55,321 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:39:55,329 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-28 21:39:55,329 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 21:39:55,331 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-28 21:39:55,331 - INFO - 收集Site數據: 最大Site號=4，實際Site數=4，16筆Site資料
2025-07-28 21:39:55,332 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 21:39:55,332 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-28 21:39:55,333 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 21:39:55,333 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-28 21:39:55,334 - INFO - ⏱️ 統一數據收集 執行時間: 14.4ms
2025-07-28 21:39:55,334 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-28 21:39:55,334 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 15.4ms
2025-07-28 21:39:55,334 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-28 21:39:55,335 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-28 21:39:55,335 - INFO -   - 填充缺失的項目編號和名稱
2025-07-28 21:39:55,335 - INFO -   - 設置紅色字體標記
2025-07-28 21:39:55,338 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 3.6ms
2025-07-28 21:39:55,339 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-28 21:39:55,339 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-28 21:39:55,339 - INFO -   - 填充預設限制值
2025-07-28 21:39:55,340 - INFO -   - 設置紅色字體標記
2025-07-28 21:39:55,346 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 7.0ms
2025-07-28 21:39:55,347 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-28 21:39:55,347 - INFO -   - 驗證數據完整性
2025-07-28 21:39:55,347 - INFO -   - 準備統一數據摘要
2025-07-28 21:39:55,347 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-28 21:39:55,348 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-28 21:39:55,348 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-28 21:39:55,350 - INFO - 設備行14: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,350 - INFO - 設備行15: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,351 - INFO - 設備行16: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,352 - INFO - 設備行18: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,353 - INFO - 設備行19: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,353 - INFO - 設備行20: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,355 - INFO - 設備行22: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,355 - INFO - 設備行23: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,356 - INFO - 設備行24: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,358 - INFO - 設備行26: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,359 - INFO - 設備行27: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,359 - INFO - 設備行28: 失敗項目Site_Check, 使用Bin值39
2025-07-28 21:39:55,360 - INFO - 重新計算並更新了17個設備的Bin值（使用第6行項目特定Bin值）
2025-07-28 21:39:55,360 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 11.5ms
2025-07-28 21:39:55,360 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 41.5ms
2025-07-28 21:39:55,361 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-28 21:39:55,361 - INFO - 應用Device2BinControl處理...
2025-07-28 21:39:55,361 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:39:55,361 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:39:55,362 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:39:55,362 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:39:55,363 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:39:55,363 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:39:55,363 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:39:55,365 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:39:55,365 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.2ms
2025-07-28 21:39:55,366 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:39:55,366 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-28 21:39:55,366 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:39:55,366 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:39:55,366 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:39:55,366 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:39:55,366 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:39:55,368 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:39:55,368 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:39:55,368 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-28 21:39:55,368 - INFO - 收集原始Bin值: 17個設備
2025-07-28 21:39:55,379 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-28 21:39:55,379 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-28 21:39:55,380 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-28 21:39:55,380 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:39:55,380 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 13.5ms
2025-07-28 21:39:55,380 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:39:55,381 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:39:55,381 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:39:55,381 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:39:55,381 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:39:55,382 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:39:55,382 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:39:55,441 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-28 21:39:55,442 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-28 21:39:55,442 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 62.6ms
2025-07-28 21:39:55,442 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 81.2ms
2025-07-28 21:39:55,443 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:39:55,443 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-28 21:39:55,444 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-28 21:39:55,444 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:39:55,444 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:39:55,444 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:39:55,445 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:39:55,445 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-28 21:39:55,445 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:39:55,445 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:39:55,445 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:39:55,446 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:39:55,446 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:39:55,446 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:39:55,446 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:39:55,446 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:39:55,446 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:39:55,447 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:39:55,447 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.3ms
2025-07-28 21:39:55,447 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:39:55,447 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:39:55,450 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:39:55,450 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.7ms
2025-07-28 21:39:55,450 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:39:55,450 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-28 21:39:55,451 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:39:55,451 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:39:55,451 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:39:55,451 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:39:55,451 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:39:55,451 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:39:55,453 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:39:55,453 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-28 21:39:55,453 - INFO - 收集原始Bin值: 17個設備
2025-07-28 21:39:55,463 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-28 21:39:55,463 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-28 21:39:55,464 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-28 21:39:55,465 - INFO - 添加原始檔案超連結: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/GMT_G2304.csv
2025-07-28 21:39:55,465 - INFO - 計算實際第4列(Definition列)項目數量: 0
2025-07-28 21:39:55,467 - INFO - Site信息檢查: total_site_no=4, good_site_n=True, site_data數量=16
2025-07-28 21:39:55,467 - INFO - 創建Site統計完成: 最大Site號=4，實際有設備的Site數=4
2025-07-28 21:39:55,467 - INFO -   Site 1: 4個設備
2025-07-28 21:39:55,467 - INFO -   Site 2: 4個設備
2025-07-28 21:39:55,467 - INFO -   Site 3: 4個設備
2025-07-28 21:39:55,468 - INFO -   Site 4: 4個設備
2025-07-28 21:39:55,468 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-28 21:39:55,468 - INFO - 填充Site 1統計完成: 4個設備
2025-07-28 21:39:55,468 - INFO - 填充Site 2統計完成: 4個設備
2025-07-28 21:39:55,469 - INFO - 填充Site 3統計完成: 4個設備
2025-07-28 21:39:55,469 - INFO - 填充Site 4統計完成: 4個設備
2025-07-28 21:39:55,469 - INFO - VBA 421-448行：設置標題行和Site統計完成，實際項目數量: 0
2025-07-28 21:39:55,471 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-28 21:39:55,471 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-28 21:39:55,472 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-28 21:39:55,472 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-28 21:39:55,472 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 21:39:55,472 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:39:55,472 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 20.6ms
2025-07-28 21:39:55,472 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:39:55,473 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:39:55,473 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:39:55,473 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:39:55,474 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:39:55,474 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:39:55,474 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:39:55,557 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-28 21:39:55,557 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-28 21:39:55,557 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 85.1ms
2025-07-28 21:39:55,558 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 112.0ms
2025-07-28 21:39:55,558 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:39:55,558 - INFO - === 統一處理管道完成 ===
2025-07-28 21:40:00,205 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-28 21:40:01,515 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\GMT_G2304_clean_converted.xlsx，耗時1309.5ms
2025-07-28 21:40:01,516 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\GMT_G2304_clean_converted.xlsx
2025-07-28 21:45:15,563 - INFO - VBA 457-469行：填充基本統計 Total=6, Pass=0, Fail=0, Yield=0%
2025-07-28 21:45:15,565 - INFO - 添加原始檔案文字（檔案不存在）: test.xlsx
2025-07-28 21:45:15,565 - INFO - VBA 362-420行（修正版）：填充了3個測試項目的Bin數據，無重複
2025-07-28 21:45:15,565 - INFO - 創建Site統計完成: 最大Site號=2，實際有設備的Site數=2
2025-07-28 21:45:15,565 - INFO -   Site 1: 3個設備
2025-07-28 21:45:15,565 - INFO -   Site 2: 3個設備
2025-07-28 21:45:15,565 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-28 21:45:15,566 - INFO - 填充Site 1統計完成: 3個設備
2025-07-28 21:45:15,566 - INFO - 填充Site 2統計完成: 3個設備
2025-07-28 21:45:15,566 - INFO - Site統計填充完成: 2個Site
2025-07-28 21:45:15,566 - INFO - 設置 AutoFilter 範圍: A6:I9
2025-07-28 21:45:15,566 - INFO - 按 B 欄由大到小排序了 3 行資料
2025-07-28 21:45:15,566 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-28 21:45:15,566 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 21:45:38,735 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-28 21:45:38,739 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_z460w82j
2025-07-28 21:45:38,739 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-28 21:45:38,744 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_z460w82j\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 21:45:38,746 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd.zip
2025-07-28 21:45:38,748 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_4tv459xn
2025-07-28 21:45:38,750 - INFO - ZIP檔案包含: ['G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd']
2025-07-28 21:45:38,775 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_4tv459xn\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-28 21:45:46,880 - INFO - 開始完整轉換...
2025-07-28 21:45:46,880 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_z460w82j\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 21:45:46,974 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-28 21:45:46,978 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-28 21:45:46,978 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:45:46,979 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-28 21:45:46,979 - INFO - 執行CTA到TMT格式轉換...
2025-07-28 21:45:53,007 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時6017.7ms
2025-07-28 21:45:54,705 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時1698.1ms
2025-07-28 21:45:54,737 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時31.9ms
2025-07-28 21:45:54,738 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時7753.9ms
2025-07-28 21:45:54,738 - INFO - 應用CTA8280轉換處理...
2025-07-28 21:45:54,739 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-28 21:45:54,739 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-28 21:45:54,793 - INFO - 找到標題行在第2行
2025-07-28 21:45:54,794 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 55.4ms
2025-07-28 21:45:54,794 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-28 21:45:54,794 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-28 21:45:54,795 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-28 21:45:54,905 - INFO - myFindedRowN=2, myColumnN=14
2025-07-28 21:46:00,834 - INFO - ⚡ 超級批量插入6行完成，耗時909.0ms
2025-07-28 21:46:00,835 - INFO - 插入了6行在最前面
2025-07-28 21:46:00,836 - INFO - ⚡ 標題行插入優化完成，耗時910.0ms
2025-07-28 21:46:00,836 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 6.04s
2025-07-28 21:46:00,836 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-28 21:46:00,836 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-28 21:46:00,836 - INFO - 清空位置 A8: "Index_No"
2025-07-28 21:46:00,836 - INFO - 清空位置 B8: "Dut_No"
2025-07-28 21:46:00,836 - INFO - 清空位置 A10: "Max"
2025-07-28 21:46:00,836 - INFO - 清空位置 B10: "Max"
2025-07-28 21:46:00,836 - INFO - 清空位置 A11: "Min"
2025-07-28 21:46:00,836 - INFO - 清空位置 B11: "Min"
2025-07-28 21:46:00,839 - INFO - ✅ 清空了6個多餘資料位置
2025-07-28 21:46:01,077 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 241.5ms
2025-07-28 21:46:01,077 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-28 21:46:01,083 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 5.9ms
2025-07-28 21:46:01,084 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-28 21:46:01,142 - INFO - 找到SW_Bin列在第6列
2025-07-28 21:46:01,203 - INFO - 處理了264個設備的數據
2025-07-28 21:46:01,204 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 119.9ms
2025-07-28 21:46:01,204 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 6.47s
2025-07-28 21:46:01,204 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-28 21:46:01,205 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-28 21:46:01,205 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:46:01,205 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:46:01,206 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:46:01,206 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:46:01,206 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:46:01,206 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:46:01,206 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:46:01,207 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:46:01,207 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:46:01,207 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:46:01,207 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:46:01,207 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:46:01,280 - INFO - 收集項目數據: 197個項目
2025-07-28 21:46:01,280 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:46:17,476 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-28 21:46:17,477 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 21:46:17,540 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-28 21:46:17,542 - INFO - 收集Site數據: 最大Site號=1，實際Site數=1，264筆Site資料
2025-07-28 21:46:17,542 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 21:46:17,543 - INFO - 收集限制值: Max 184個, Min 184個
2025-07-28 21:46:17,544 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 21:46:17,545 - INFO - 設置第6行項目編號: 從第3列開始，共197個項目
2025-07-28 21:46:17,546 - INFO - ⏱️ 統一數據收集 執行時間: 16.34s
2025-07-28 21:46:17,546 - INFO - ✅ 統一數據收集完成: 項目197個, 設備264個, Site1個
2025-07-28 21:46:17,546 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 16.34s
2025-07-28 21:46:17,547 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-28 21:46:17,547 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-28 21:46:17,547 - INFO -   - 填充缺失的項目編號和名稱
2025-07-28 21:46:17,547 - INFO -   - 設置紅色字體標記
2025-07-28 21:46:17,576 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 29.5ms
2025-07-28 21:46:17,577 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-28 21:46:17,577 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-28 21:46:17,578 - INFO -   - 填充預設限制值
2025-07-28 21:46:17,578 - INFO -   - 設置紅色字體標記
2025-07-28 21:46:17,597 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 19.5ms
2025-07-28 21:46:17,597 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-28 21:46:17,598 - INFO -   - 驗證數據完整性
2025-07-28 21:46:17,598 - INFO -   - 準備統一數據摘要
2025-07-28 21:46:17,598 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-28 21:46:17,598 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-28 21:46:17,599 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-28 21:46:17,603 - INFO - 設備行14: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,612 - INFO - 設備行18: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,621 - INFO - 設備行23: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-28 21:46:17,634 - INFO - 設備行29: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,640 - INFO - 設備行32: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,651 - INFO - 設備行38: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,662 - INFO - 設備行43: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,666 - INFO - 設備行45: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,686 - INFO - 設備行55: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,691 - INFO - 設備行58: 失敗項目CP_MTP, 使用Bin值78
2025-07-28 21:46:17,698 - INFO - 設備行61: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,704 - INFO - 設備行64: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,706 - INFO - 設備行65: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,729 - INFO - 設備行77: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,754 - INFO - 設備行90: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,766 - INFO - 設備行96: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,768 - INFO - 設備行97: 失敗項目OS_Short, 使用Bin值40
2025-07-28 21:46:17,771 - INFO - 設備行98: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,774 - INFO - 設備行100: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,779 - INFO - 設備行102: 失敗項目Min_Duty, 使用Bin值196
2025-07-28 21:46:17,785 - INFO - 設備行105: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,789 - INFO - 設備行107: 失敗項目VREF_bf, 使用Bin值136
2025-07-28 21:46:17,796 - INFO - 設備行110: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,802 - INFO - 設備行113: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-28 21:46:17,810 - INFO - 設備行117: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,813 - INFO - 設備行118: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 21:46:17,815 - INFO - 設備行119: 失敗項目OS_Short, 使用Bin值40
2025-07-28 21:46:17,820 - INFO - 設備行121: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,823 - INFO - 設備行123: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,830 - INFO - 設備行126: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 21:46:17,837 - INFO - 設備行130: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-28 21:46:17,839 - INFO - 設備行131: 失敗項目OS_Short, 使用Bin值40
2025-07-28 21:46:17,842 - INFO - 設備行132: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,844 - INFO - 設備行133: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,851 - INFO - 設備行136: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,853 - INFO - 設備行137: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,856 - INFO - 設備行138: 失敗項目CR_CH3, 使用Bin值64
2025-07-28 21:46:17,858 - INFO - 設備行139: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,863 - INFO - 設備行141: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,866 - INFO - 設備行142: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,875 - INFO - 設備行147: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-28 21:46:17,889 - INFO - 設備行154: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,899 - INFO - 設備行159: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 21:46:17,903 - INFO - 設備行161: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-28 21:46:17,909 - INFO - 設備行164: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-28 21:46:17,931 - INFO - 設備行176: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:17,938 - INFO - 設備行179: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,947 - INFO - 設備行184: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,956 - INFO - 設備行188: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,958 - INFO - 設備行189: 失敗項目CR_PWM, 使用Bin值71
2025-07-28 21:46:17,970 - INFO - 設備行195: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 21:46:17,972 - INFO - 設備行196: 失敗項目IOC_code10, 使用Bin值183
2025-07-28 21:46:17,974 - INFO - 設備行197: 失敗項目CR_CH2, 使用Bin值65
2025-07-28 21:46:17,987 - INFO - 設備行204: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:17,997 - INFO - 設備行209: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-28 21:46:17,999 - INFO - 設備行210: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:18,013 - INFO - 設備行217: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:18,016 - INFO - 設備行219: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 21:46:18,030 - INFO - 設備行226: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-28 21:46:18,034 - INFO - 設備行228: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:18,039 - INFO - 設備行230: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:18,056 - INFO - 設備行239: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-28 21:46:18,095 - INFO - 設備行261: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 21:46:18,121 - INFO - 重新計算並更新了264個設備的Bin值（使用第6行項目特定Bin值）
2025-07-28 21:46:18,122 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 524.5ms
2025-07-28 21:46:18,122 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 16.92s
2025-07-28 21:46:18,123 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-28 21:46:18,123 - INFO - 應用Device2BinControl處理...
2025-07-28 21:46:18,124 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:46:18,124 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:46:18,124 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:46:18,124 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:46:18,125 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:46:18,125 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:46:18,125 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:46:18,304 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:46:18,305 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 179.8ms
2025-07-28 21:46:18,305 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:46:18,305 - INFO - ✅ 數據摘要: 項目197個, 設備264個, Site1個
2025-07-28 21:46:18,306 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:46:18,306 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:46:18,306 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:46:18,306 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:46:18,307 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:46:18,307 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:46:18,307 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:46:18,308 - INFO - 使用統一數據: 設備264個, 項目197個
2025-07-28 21:46:18,308 - INFO - 收集原始Bin值: 264個設備
2025-07-28 21:46:18,877 - INFO - 應用染色邏輯: 63個設備的失敗項目
2025-07-28 21:46:18,878 - INFO - Bin統計: Pass=201, Fail=63, Total=264
2025-07-28 21:46:18,879 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-28 21:46:18,879 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:46:18,879 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 572.6ms
2025-07-28 21:46:18,880 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:46:18,880 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:46:18,880 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:46:18,881 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:46:18,881 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:46:18,881 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:46:18,881 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:46:21,358 - INFO - ⚡ 超級批量字體設置完成，總共處理52041個cell
2025-07-28 21:46:21,358 - INFO - 設置數據區域字體顏色: 行13-276, 列2-199
2025-07-28 21:46:21,358 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 2.48s
2025-07-28 21:46:21,359 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 3.24s
2025-07-28 21:46:21,359 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:46:21,360 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-28 21:46:21,360 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-28 21:46:21,360 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:46:21,360 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:46:21,361 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:46:21,361 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:46:21,361 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-28 21:46:21,361 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:46:21,362 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:46:21,362 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:46:21,362 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:46:21,362 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:46:21,363 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:46:21,363 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 21:46:21,364 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 21:46:21,364 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 21:46:21,364 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 21:46:21,364 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 21:46:21,364 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 21:46:21,365 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 21:46:21,542 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 21:46:21,543 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 178.0ms
2025-07-28 21:46:21,544 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 21:46:21,544 - INFO - ✅ 數據摘要: 項目197個, 設備264個, Site1個
2025-07-28 21:46:21,544 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 21:46:21,544 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 21:46:21,544 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 21:46:21,545 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 21:46:21,546 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 21:46:21,546 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 21:46:21,546 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 21:46:21,546 - INFO - 使用統一數據: 設備264個, 項目197個
2025-07-28 21:46:21,547 - INFO - 收集原始Bin值: 264個設備
2025-07-28 21:46:22,119 - INFO - 應用染色邏輯: 63個設備的失敗項目
2025-07-28 21:46:22,120 - INFO - Bin統計: Pass=201, Fail=63, Total=264
2025-07-28 21:46:22,121 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=201, Fail=63, Yield=76.14%
2025-07-28 21:46:22,122 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_z460w82j\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 21:46:22,188 - INFO - VBA 362-420行（修正版）：填充了16個測試項目的Bin數據，無重複
2025-07-28 21:46:22,188 - INFO - 創建Site統計完成: 最大Site號=1，實際有設備的Site數=1
2025-07-28 21:46:22,189 - INFO -   Site 1: 264個設備
2025-07-28 21:46:22,189 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-28 21:46:22,190 - INFO - 填充Site 1統計完成: 264個設備
2025-07-28 21:46:22,190 - INFO - Site統計填充完成: 1個Site
2025-07-28 21:46:22,190 - INFO - 設置 AutoFilter 範圍: A6:G22
2025-07-28 21:46:22,192 - INFO - 按 B 欄由大到小排序了 16 行資料
2025-07-28 21:46:22,193 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-28 21:46:22,193 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 21:46:22,194 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 21:46:22,194 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 650.0ms
2025-07-28 21:46:22,195 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 21:46:22,195 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 21:46:22,195 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 21:46:22,196 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 21:46:22,196 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 21:46:22,196 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 21:46:22,196 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 21:46:25,810 - INFO - ⚡ 超級批量字體設置完成，總共處理52041個cell
2025-07-28 21:46:25,811 - INFO - 設置數據區域字體顏色: 行13-276, 列2-199
2025-07-28 21:46:25,811 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 3.62s
2025-07-28 21:46:25,811 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 4.45s
2025-07-28 21:46:25,811 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 21:46:25,812 - INFO - === 統一處理管道完成 ===
2025-07-28 21:46:47,862 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-28 21:46:53,065 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時5202.6ms
2025-07-28 21:46:53,065 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-28 21:46:53,068 - INFO - 開始完整轉換...
2025-07-28 21:46:53,068 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_4tv459xn\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-28 21:46:53,251 - INFO - 成功讀取CSV文件，大小: (7971, 110)
2025-07-28 21:46:53,252 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-28 21:46:53,252 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 21:46:53,253 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-28 21:46:53,253 - INFO - 創建TMT格式工作簿...
2025-07-28 21:47:01,619 - INFO - ⚡ 優化寫入完成: 7971行 × 110列，耗時8364.1ms
2025-07-28 21:47:01,623 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-28 21:47:01,624 - INFO - TMT格式不需要sum分頁
2025-07-28 21:47:01,624 - INFO - TMT工作簿創建完成：僅Data11工作表 7971行，無sum分頁
2025-07-28 21:47:01,624 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 21:47:01,625 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 21:47:01,625 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 21:47:01,626 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 21:47:01,626 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 21:47:01,626 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 21:47:01,626 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 21:47:01,627 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 21:47:01,627 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 21:47:01,627 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 21:47:01,627 - INFO - 🔍 開始統一數據收集...
2025-07-28 21:47:01,628 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 21:47:01,931 - INFO - 收集項目數據: 108個項目
2025-07-28 21:47:01,931 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 21:51:24,510 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_z460w82j
2025-07-28 21:51:24,630 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_4tv459xn
2025-07-28 22:01:11,312 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/OneDrive/桌面/Python Tool/AICA/test - 複製 (2)/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-28 22:01:11,313 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_sv5cwqag
2025-07-28 22:01:11,314 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-28 22:01:11,317 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_sv5cwqag\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 22:01:12,192 - INFO - 開始完整轉換...
2025-07-28 22:01:12,192 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_sv5cwqag\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 22:01:12,273 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-28 22:01:12,276 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-28 22:01:12,276 - INFO - === 開始7步驟統一處理管道 ===
2025-07-28 22:01:12,277 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-28 22:01:12,277 - INFO - 執行CTA到TMT格式轉換...
2025-07-28 22:01:17,887 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時5603.3ms
2025-07-28 22:01:19,695 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時1803.5ms
2025-07-28 22:01:19,725 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時30.3ms
2025-07-28 22:01:19,726 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時7445.7ms
2025-07-28 22:01:19,726 - INFO - 應用CTA8280轉換處理...
2025-07-28 22:01:19,726 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-28 22:01:19,726 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-28 22:01:19,784 - INFO - 找到標題行在第2行
2025-07-28 22:01:19,785 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 58.5ms
2025-07-28 22:01:19,785 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-28 22:01:19,785 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-28 22:01:19,785 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-28 22:01:19,909 - INFO - myFindedRowN=2, myColumnN=14
2025-07-28 22:01:27,107 - INFO - ⚡ 超級批量插入6行完成，耗時1135.6ms
2025-07-28 22:01:27,108 - INFO - 插入了6行在最前面
2025-07-28 22:01:27,108 - INFO - ⚡ 標題行插入優化完成，耗時1136.6ms
2025-07-28 22:01:27,108 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 7.32s
2025-07-28 22:01:27,109 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-28 22:01:27,109 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-28 22:01:27,109 - INFO - 清空位置 A8: "Index_No"
2025-07-28 22:01:27,110 - INFO - 清空位置 B8: "Dut_No"
2025-07-28 22:01:27,110 - INFO - 清空位置 A10: "Max"
2025-07-28 22:01:27,110 - INFO - 清空位置 B10: "Max"
2025-07-28 22:01:27,110 - INFO - 清空位置 A11: "Min"
2025-07-28 22:01:27,112 - INFO - 清空位置 B11: "Min"
2025-07-28 22:01:27,112 - INFO - ✅ 清空了6個多餘資料位置
2025-07-28 22:01:27,433 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 323.8ms
2025-07-28 22:01:27,433 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-28 22:01:27,439 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 6.0ms
2025-07-28 22:01:27,440 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-28 22:01:27,527 - INFO - 找到SW_Bin列在第6列
2025-07-28 22:01:27,605 - INFO - 處理了264個設備的數據
2025-07-28 22:01:27,605 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 164.5ms
2025-07-28 22:01:27,605 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 7.88s
2025-07-28 22:01:27,606 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-28 22:01:27,606 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-28 22:01:27,606 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-28 22:01:27,606 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-28 22:01:27,607 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-28 22:01:27,607 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-28 22:01:27,607 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-28 22:01:27,607 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-28 22:01:27,608 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-28 22:01:27,608 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-28 22:01:27,608 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-28 22:01:27,608 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-28 22:01:27,609 - INFO - 🔍 開始統一數據收集...
2025-07-28 22:01:27,609 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-28 22:02:04,705 - INFO - 收集項目數據: 606個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-28 22:02:04,706 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-28 22:02:22,124 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-28 22:02:22,125 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-28 22:02:22,192 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-28 22:02:22,194 - INFO - 收集Site數據: 最大Site號=1，實際Site數=1，264筆Site資料
2025-07-28 22:02:22,194 - INFO - 🔄 6.1.4 收集限制值...
2025-07-28 22:02:22,197 - INFO - 收集限制值: Max 593個, Min 593個
2025-07-28 22:02:22,197 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-28 22:02:22,200 - INFO - 設置第6行項目編號: 從第3列開始，共606個項目
2025-07-28 22:02:22,201 - INFO - ⏱️ 統一數據收集 執行時間: 54.59s
2025-07-28 22:02:22,201 - INFO - ✅ 統一數據收集完成: 項目606個, 設備264個, Site1個
2025-07-28 22:02:22,201 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 54.59s
2025-07-28 22:02:22,201 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-28 22:02:22,201 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-28 22:02:22,202 - INFO -   - 填充缺失的項目編號和名稱
2025-07-28 22:02:22,202 - INFO -   - 設置紅色字體標記
2025-07-28 22:02:22,297 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 96.1ms
2025-07-28 22:02:22,298 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-28 22:02:22,298 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-28 22:02:22,298 - INFO -   - 填充預設限制值
2025-07-28 22:02:22,298 - INFO -   - 設置紅色字體標記
2025-07-28 22:02:22,340 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 41.7ms
2025-07-28 22:02:22,340 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-28 22:02:22,340 - INFO -   - 驗證數據完整性
2025-07-28 22:02:22,341 - INFO -   - 準備統一數據摘要
2025-07-28 22:02:22,341 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-28 22:02:22,341 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-28 22:02:22,342 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-28 22:02:22,355 - INFO - 設備行14: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:22,362 - INFO - 設備行15: 失敗項目ISHDN, 使用Bin值353
2025-07-28 22:02:22,374 - INFO - 設備行17: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,380 - INFO - 設備行18: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,400 - INFO - 設備行21: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,414 - INFO - 設備行23: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-28 22:02:22,427 - INFO - 設備行25: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,433 - INFO - 設備行26: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,450 - INFO - 設備行28: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,458 - INFO - 設備行29: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,470 - INFO - 設備行31: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,477 - INFO - 設備行32: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,496 - INFO - 設備行34: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,505 - INFO - 設備行35: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,523 - INFO - 設備行38: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,530 - INFO - 設備行39: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,550 - INFO - 設備行42: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,557 - INFO - 設備行43: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,565 - INFO - 設備行44: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,572 - INFO - 設備行45: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,589 - INFO - 設備行47: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:22,606 - INFO - 設備行48: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,624 - INFO - 設備行51: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:22,647 - INFO - 設備行55: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:22,654 - INFO - 設備行56: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,667 - INFO - 設備行58: 失敗項目CP_MTP, 使用Bin值78
2025-07-28 22:02:22,684 - INFO - 設備行61: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,703 - INFO - 設備行64: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,709 - INFO - 設備行65: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:22,715 - INFO - 設備行66: 失敗項目IQ_min, 使用Bin值350
2025-07-28 22:02:22,722 - INFO - 設備行67: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:22,733 - INFO - 設備行69: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:22,751 - INFO - 設備行72: 失敗項目A0_IIL, 使用Bin值326
2025-07-28 22:02:22,769 - INFO - 設備行75: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,775 - INFO - 設備行76: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,782 - INFO - 設備行77: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,799 - INFO - 設備行80: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,818 - INFO - 設備行83: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:22,825 - INFO - 設備行84: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:22,828 - INFO - 設備行85: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,856 - INFO - 設備行88: 失敗項目iCh4_OVP_Aft, 使用Bin值231
2025-07-28 22:02:22,865 - INFO - 設備行89: 失敗項目ISHDN, 使用Bin值353
2025-07-28 22:02:22,871 - INFO - 設備行90: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,877 - INFO - 設備行91: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,897 - INFO - 設備行94: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,908 - INFO - 設備行96: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:22,915 - INFO - 設備行97: 失敗項目OS_Short, 使用Bin值40
2025-07-28 22:02:22,923 - INFO - 設備行98: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,931 - INFO - 設備行99: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,937 - INFO - 設備行100: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,944 - INFO - 設備行101: 失敗項目ILED1_PwmHi, 使用Bin值365
2025-07-28 22:02:22,951 - INFO - 設備行102: 失敗項目Min_Duty, 使用Bin值196
2025-07-28 22:02:22,969 - INFO - 設備行105: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:22,976 - INFO - 設備行106: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,983 - INFO - 設備行107: 失敗項目VREF_bf, 使用Bin值136
2025-07-28 22:02:22,989 - INFO - 設備行108: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:22,997 - INFO - 設備行109: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:23,003 - INFO - 設備行110: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,010 - INFO - 設備行111: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,024 - INFO - 設備行113: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-28 22:02:23,033 - INFO - 設備行114: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,040 - INFO - 設備行115: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:23,047 - INFO - 設備行116: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,053 - INFO - 設備行117: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,059 - INFO - 設備行118: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 22:02:23,066 - INFO - 設備行119: 失敗項目OS_Short, 使用Bin值40
2025-07-28 22:02:23,074 - INFO - 設備行120: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,082 - INFO - 設備行121: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,089 - INFO - 設備行122: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,095 - INFO - 設備行123: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,101 - INFO - 設備行124: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:23,119 - INFO - 設備行125: 失敗項目ATPG_test, 使用Bin值485
2025-07-28 22:02:23,126 - INFO - 設備行126: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 22:02:23,133 - INFO - 設備行127: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:23,154 - INFO - 設備行130: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-28 22:02:23,160 - INFO - 設備行131: 失敗項目OS_Short, 使用Bin值40
2025-07-28 22:02:23,166 - INFO - 設備行132: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,174 - INFO - 設備行133: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,186 - INFO - 設備行135: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,192 - INFO - 設備行136: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,197 - INFO - 設備行137: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,203 - INFO - 設備行138: 失敗項目CR_CH3, 使用Bin值64
2025-07-28 22:02:23,210 - INFO - 設備行139: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,216 - INFO - 設備行140: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,222 - INFO - 設備行141: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,229 - INFO - 設備行142: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,235 - INFO - 設備行143: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,252 - INFO - 設備行146: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,259 - INFO - 設備行147: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-28 22:02:23,265 - INFO - 設備行148: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,276 - INFO - 設備行150: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,293 - INFO - 設備行153: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,299 - INFO - 設備行154: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,319 - INFO - 設備行157: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,327 - INFO - 設備行158: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,333 - INFO - 設備行159: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 22:02:23,339 - INFO - 設備行160: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,346 - INFO - 設備行161: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-28 22:02:23,376 - INFO - 設備行164: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-28 22:02:23,381 - INFO - 設備行165: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,400 - INFO - 設備行168: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,412 - INFO - 設備行170: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,422 - INFO - 設備行172: 失敗項目ISHDN, 使用Bin值353
2025-07-28 22:02:23,434 - INFO - 設備行174: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,447 - INFO - 設備行176: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,455 - INFO - 設備行177: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:23,461 - INFO - 設備行178: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,468 - INFO - 設備行179: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,491 - INFO - 設備行183: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,497 - INFO - 設備行184: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,503 - INFO - 設備行185: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,506 - INFO - 設備行186: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,521 - INFO - 設備行188: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,528 - INFO - 設備行189: 失敗項目CR_PWM, 使用Bin值71
2025-07-28 22:02:23,546 - INFO - 設備行192: 失敗項目ATPG_test, 使用Bin值485
2025-07-28 22:02:23,552 - INFO - 設備行193: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,565 - INFO - 設備行195: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-28 22:02:23,571 - INFO - 設備行196: 失敗項目IOC_code10, 使用Bin值183
2025-07-28 22:02:23,577 - INFO - 設備行197: 失敗項目CR_CH2, 使用Bin值65
2025-07-28 22:02:23,593 - INFO - 設備行200: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,601 - INFO - 設備行201: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,630 - INFO - 設備行204: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,636 - INFO - 設備行205: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,643 - INFO - 設備行206: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,649 - INFO - 設備行207: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,657 - INFO - 設備行208: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,663 - INFO - 設備行209: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-28 22:02:23,669 - INFO - 設備行210: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,695 - INFO - 設備行214: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,702 - INFO - 設備行215: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,714 - INFO - 設備行217: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,726 - INFO - 設備行219: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-28 22:02:23,757 - INFO - 設備行225: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,768 - INFO - 設備行226: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-28 22:02:23,780 - INFO - 設備行228: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,786 - INFO - 設備行229: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,792 - INFO - 設備行230: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:23,798 - INFO - 設備行231: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,804 - INFO - 設備行232: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-28 22:02:23,823 - INFO - 設備行235: 失敗項目READ_0X1D_Aft, 使用Bin值261
2025-07-28 22:02:23,829 - INFO - 設備行236: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-28 22:02:23,836 - INFO - 設備行237: 失敗項目ISHDN, 使用Bin值353
2025-07-28 22:02:23,848 - INFO - 設備行239: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-28 22:02:23,856 - INFO - 設備行241: 失敗項目ATPG_test, 使用Bin值485
2025-07-28 22:02:23,873 - INFO - 設備行242: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,978 - INFO - 設備行259: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-28 22:02:23,988 - INFO - 設備行261: 失敗項目MC_ISET, 使用Bin值179
2025-07-28 22:02:24,072 - INFO - 重新計算並更新了264個設備的Bin值（使用第6行項目特定Bin值）
2025-07-28 22:02:24,073 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 1.73s
2025-07-28 22:02:24,073 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 56.46s
2025-07-28 22:02:24,073 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-28 22:02:24,073 - INFO - 應用Device2BinControl處理...
2025-07-28 22:02:24,074 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 22:02:24,074 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 22:02:24,074 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 22:02:24,074 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 22:02:24,074 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-28 22:02:24,075 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 22:02:24,075 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 22:02:24,269 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 22:02:24,270 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 195.2ms
2025-07-28 22:02:24,270 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 22:02:24,270 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-28 22:02:24,270 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 22:02:24,271 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 22:02:24,271 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 22:02:24,271 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 22:02:24,271 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 22:02:24,271 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 22:02:24,273 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 22:02:24,273 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-28 22:02:24,273 - INFO - 收集原始Bin值: 264個設備
2025-07-28 22:02:26,151 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-28 22:02:26,152 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-28 22:02:26,152 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-28 22:02:26,152 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 22:02:26,154 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.88s
2025-07-28 22:02:26,155 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 22:02:26,155 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 22:02:26,155 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 22:02:26,155 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 22:02:26,156 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 22:02:26,156 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 22:02:26,156 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 22:02:33,752 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-28 22:02:33,753 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-28 22:02:33,753 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 7.60s
2025-07-28 22:02:33,753 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 9.68s
2025-07-28 22:02:33,754 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 22:02:33,754 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-28 22:02:33,754 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-28 22:02:33,754 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 22:02:33,755 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 22:02:33,755 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 22:02:33,756 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 22:02:33,756 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-28 22:02:33,756 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 22:02:33,756 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 22:02:33,756 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 22:02:33,756 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 22:02:33,757 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 22:02:33,757 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 22:02:33,757 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-28 22:02:33,757 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-28 22:02:33,757 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-28 22:02:33,758 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-28 22:02:33,758 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: 1.2ms
2025-07-28 22:02:33,758 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-28 22:02:33,759 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-28 22:02:33,981 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-28 22:02:33,981 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 222.0ms
2025-07-28 22:02:33,981 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-28 22:02:33,981 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-28 22:02:33,981 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-28 22:02:33,984 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-28 22:02:33,984 - INFO -   - 7.1.1 收集原始Bin值
2025-07-28 22:02:33,985 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-28 22:02:33,985 - INFO -   - 7.1.3 計算Bin統計
2025-07-28 22:02:33,985 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-28 22:02:33,985 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-28 22:02:33,986 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-28 22:02:33,986 - INFO - 收集原始Bin值: 264個設備
2025-07-28 22:02:36,027 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-28 22:02:36,027 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-28 22:02:36,029 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=117, Fail=147, Yield=44.32%
2025-07-28 22:02:36,030 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_sv5cwqag\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-28 22:02:36,101 - INFO - VBA 362-420行（修正版）：填充了26個測試項目的Bin數據，無重複
2025-07-28 22:02:36,102 - INFO - 創建Site統計完成: 最大Site號=1，實際有設備的Site數=1
2025-07-28 22:02:36,103 - INFO -   Site 1: 264個設備
2025-07-28 22:02:36,103 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-28 22:02:36,103 - INFO - 填充Site 1統計完成: 264個設備
2025-07-28 22:02:36,103 - INFO - Site統計填充完成: 1個Site
2025-07-28 22:02:36,105 - INFO - 設置 AutoFilter 範圍: A6:G32
2025-07-28 22:02:36,108 - INFO - 按 B 欄由大到小排序了 26 行資料
2025-07-28 22:02:36,108 - INFO - 自動調整D列寬度: 17.6 (基於最大內容長度: 13)
2025-07-28 22:02:36,108 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-28 22:02:36,108 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-28 22:02:36,108 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 2.12s
2025-07-28 22:02:36,108 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-28 22:02:36,108 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-28 22:02:36,108 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-28 22:02:36,108 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-28 22:02:36,108 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-28 22:02:36,112 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-28 22:02:36,112 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-28 22:02:47,546 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-28 22:02:47,547 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-28 22:02:47,547 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 11.44s
2025-07-28 22:02:47,547 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 13.79s
2025-07-28 22:02:47,547 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-28 22:02:47,549 - INFO - === 統一處理管道完成 ===
2025-07-28 22:03:10,284 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-28 22:03:15,730 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時5448.3ms
2025-07-28 22:03:15,730 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\OneDrive\桌面\Python Tool\AICA\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-28 22:04:23,314 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_sv5cwqag
2025-07-29 08:51:31,304 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-29 08:51:31,308 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_gvxwmn2a
2025-07-29 08:51:31,309 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-29 08:51:31,315 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_gvxwmn2a\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 08:51:31,317 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd.zip
2025-07-29 08:51:31,317 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_q1wjj6el
2025-07-29 08:51:31,318 - INFO - ZIP檔案包含: ['G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd']
2025-07-29 08:51:31,345 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_q1wjj6el\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-29 08:51:32,078 - INFO - 開始完整轉換...
2025-07-29 08:51:32,079 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_gvxwmn2a\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 08:51:32,231 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-29 08:51:32,236 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-29 08:51:32,236 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 08:51:32,236 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-29 08:51:32,237 - INFO - 執行CTA到TMT格式轉換...
2025-07-29 08:51:37,019 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時4772.1ms
2025-07-29 08:51:38,403 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時1380.6ms
2025-07-29 08:51:38,429 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時24.6ms
2025-07-29 08:51:38,430 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時6187.1ms
2025-07-29 08:51:38,430 - INFO - 應用CTA8280轉換處理...
2025-07-29 08:51:38,430 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-29 08:51:38,431 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-29 08:51:38,434 - INFO - 找到標題行在第2行
2025-07-29 08:51:38,435 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 4.0ms
2025-07-29 08:51:38,435 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-29 08:51:38,436 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-29 08:51:38,436 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-29 08:51:38,528 - INFO - myFindedRowN=2, myColumnN=14
2025-07-29 08:51:42,708 - INFO - ⚡ 超級批量插入6行完成，耗時754.2ms
2025-07-29 08:51:42,708 - INFO - 插入了6行在最前面
2025-07-29 08:51:42,709 - INFO - ⚡ 標題行插入優化完成，耗時754.2ms
2025-07-29 08:51:42,709 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 4.27s
2025-07-29 08:51:42,710 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-29 08:51:42,710 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-29 08:51:42,711 - INFO - 清空位置 A8: "Index_No"
2025-07-29 08:51:42,711 - INFO - 清空位置 B8: "Dut_No"
2025-07-29 08:51:42,711 - INFO - 清空位置 A10: "Max"
2025-07-29 08:51:42,712 - INFO - 清空位置 B10: "Max"
2025-07-29 08:51:42,712 - INFO - 清空位置 A11: "Min"
2025-07-29 08:51:42,712 - INFO - 清空位置 B11: "Min"
2025-07-29 08:51:42,713 - INFO - ✅ 清空了6個多餘資料位置
2025-07-29 08:51:42,821 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 111.1ms
2025-07-29 08:51:42,821 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-29 08:51:42,823 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 1.9ms
2025-07-29 08:51:42,823 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-29 08:51:42,871 - INFO - 找到SW_Bin列在第6列
2025-07-29 08:51:42,882 - INFO - 處理了264個設備的數據
2025-07-29 08:51:42,883 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 60.0ms
2025-07-29 08:51:42,883 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 4.45s
2025-07-29 08:51:42,884 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-29 08:51:42,884 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-29 08:51:42,884 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 08:51:42,885 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 08:51:42,885 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 08:51:42,885 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 08:51:42,886 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 08:51:42,886 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 08:51:42,886 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 08:51:42,887 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 08:51:42,887 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 08:51:42,887 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 08:51:42,887 - INFO - 🔍 開始統一數據收集...
2025-07-29 08:51:42,888 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 08:51:44,806 - INFO - 收集項目數據: 606個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 08:51:44,807 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 08:51:57,632 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-29 08:51:57,633 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 08:51:57,680 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-29 08:51:57,681 - INFO - 收集Site數據: 最大Site號=1，實際Site數=1，264筆Site資料
2025-07-29 08:51:57,682 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 08:51:57,684 - INFO - 收集限制值: Max 593個, Min 593個
2025-07-29 08:51:57,685 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 08:51:57,687 - INFO - 設置第6行項目編號: 從第3列開始，共606個項目
2025-07-29 08:51:57,687 - INFO - ⏱️ 統一數據收集 執行時間: 14.80s
2025-07-29 08:51:57,687 - INFO - ✅ 統一數據收集完成: 項目606個, 設備264個, Site1個
2025-07-29 08:51:57,687 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 14.80s
2025-07-29 08:51:57,687 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 08:51:57,688 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 08:51:57,688 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 08:51:57,688 - INFO -   - 設置紅色字體標記
2025-07-29 08:51:57,751 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 63.0ms
2025-07-29 08:51:57,751 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 08:51:57,751 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 08:51:57,751 - INFO -   - 填充預設限制值
2025-07-29 08:51:57,751 - INFO -   - 設置紅色字體標記
2025-07-29 08:51:57,775 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 23.6ms
2025-07-29 08:51:57,775 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 08:51:57,776 - INFO -   - 驗證數據完整性
2025-07-29 08:51:57,776 - INFO -   - 準備統一數據摘要
2025-07-29 08:51:57,776 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 08:51:57,776 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 08:51:57,777 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 08:51:57,785 - INFO - 設備行14: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:57,790 - INFO - 設備行15: 失敗項目ISHDN, 使用Bin值353
2025-07-29 08:51:57,798 - INFO - 設備行17: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,802 - INFO - 設備行18: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,814 - INFO - 設備行21: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,823 - INFO - 設備行23: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-29 08:51:57,832 - INFO - 設備行25: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,836 - INFO - 設備行26: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,845 - INFO - 設備行28: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,849 - INFO - 設備行29: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,858 - INFO - 設備行31: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,862 - INFO - 設備行32: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,870 - INFO - 設備行34: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,875 - INFO - 設備行35: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,888 - INFO - 設備行38: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,893 - INFO - 設備行39: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,905 - INFO - 設備行42: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,910 - INFO - 設備行43: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,914 - INFO - 設備行44: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,918 - INFO - 設備行45: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,927 - INFO - 設備行47: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:57,931 - INFO - 設備行48: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,943 - INFO - 設備行51: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:57,961 - INFO - 設備行55: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:57,965 - INFO - 設備行56: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:57,974 - INFO - 設備行58: 失敗項目CP_MTP, 使用Bin值78
2025-07-29 08:51:57,986 - INFO - 設備行61: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:57,998 - INFO - 設備行64: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,003 - INFO - 設備行65: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,007 - INFO - 設備行66: 失敗項目IQ_min, 使用Bin值350
2025-07-29 08:51:58,012 - INFO - 設備行67: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,021 - INFO - 設備行69: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,034 - INFO - 設備行72: 失敗項目A0_IIL, 使用Bin值326
2025-07-29 08:51:58,046 - INFO - 設備行75: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,050 - INFO - 設備行76: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,055 - INFO - 設備行77: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,068 - INFO - 設備行80: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,081 - INFO - 設備行83: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,085 - INFO - 設備行84: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,090 - INFO - 設備行85: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,103 - INFO - 設備行88: 失敗項目iCh4_OVP_Aft, 使用Bin值231
2025-07-29 08:51:58,106 - INFO - 設備行89: 失敗項目ISHDN, 使用Bin值353
2025-07-29 08:51:58,111 - INFO - 設備行90: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,115 - INFO - 設備行91: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,128 - INFO - 設備行94: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,137 - INFO - 設備行96: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,141 - INFO - 設備行97: 失敗項目OS_Short, 使用Bin值40
2025-07-29 08:51:58,145 - INFO - 設備行98: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,150 - INFO - 設備行99: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,155 - INFO - 設備行100: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,160 - INFO - 設備行101: 失敗項目ILED1_PwmHi, 使用Bin值365
2025-07-29 08:51:58,163 - INFO - 設備行102: 失敗項目Min_Duty, 使用Bin值196
2025-07-29 08:51:58,176 - INFO - 設備行105: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,181 - INFO - 設備行106: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,185 - INFO - 設備行107: 失敗項目VREF_bf, 使用Bin值136
2025-07-29 08:51:58,190 - INFO - 設備行108: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,195 - INFO - 設備行109: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,201 - INFO - 設備行110: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,206 - INFO - 設備行111: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,215 - INFO - 設備行113: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-29 08:51:58,219 - INFO - 設備行114: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,223 - INFO - 設備行115: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,227 - INFO - 設備行116: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,232 - INFO - 設備行117: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,236 - INFO - 設備行118: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 08:51:58,240 - INFO - 設備行119: 失敗項目OS_Short, 使用Bin值40
2025-07-29 08:51:58,245 - INFO - 設備行120: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,249 - INFO - 設備行121: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,254 - INFO - 設備行122: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,258 - INFO - 設備行123: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,263 - INFO - 設備行124: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,268 - INFO - 設備行125: 失敗項目ATPG_test, 使用Bin值485
2025-07-29 08:51:58,272 - INFO - 設備行126: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 08:51:58,277 - INFO - 設備行127: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,289 - INFO - 設備行130: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-29 08:51:58,294 - INFO - 設備行131: 失敗項目OS_Short, 使用Bin值40
2025-07-29 08:51:58,299 - INFO - 設備行132: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,303 - INFO - 設備行133: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,312 - INFO - 設備行135: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,317 - INFO - 設備行136: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,321 - INFO - 設備行137: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,325 - INFO - 設備行138: 失敗項目CR_CH3, 使用Bin值64
2025-07-29 08:51:58,330 - INFO - 設備行139: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,334 - INFO - 設備行140: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,339 - INFO - 設備行141: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,342 - INFO - 設備行142: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,346 - INFO - 設備行143: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,359 - INFO - 設備行146: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,364 - INFO - 設備行147: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-29 08:51:58,368 - INFO - 設備行148: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,377 - INFO - 設備行150: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,390 - INFO - 設備行153: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,394 - INFO - 設備行154: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,407 - INFO - 設備行157: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,411 - INFO - 設備行158: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,415 - INFO - 設備行159: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 08:51:58,420 - INFO - 設備行160: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,424 - INFO - 設備行161: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-29 08:51:58,438 - INFO - 設備行164: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-29 08:51:58,443 - INFO - 設備行165: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,456 - INFO - 設備行168: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,463 - INFO - 設備行170: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,472 - INFO - 設備行172: 失敗項目ISHDN, 使用Bin值353
2025-07-29 08:51:58,481 - INFO - 設備行174: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,490 - INFO - 設備行176: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,495 - INFO - 設備行177: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,499 - INFO - 設備行178: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,504 - INFO - 設備行179: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,520 - INFO - 設備行183: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,525 - INFO - 設備行184: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,529 - INFO - 設備行185: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,534 - INFO - 設備行186: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,552 - INFO - 設備行188: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,558 - INFO - 設備行189: 失敗項目CR_PWM, 使用Bin值71
2025-07-29 08:51:58,573 - INFO - 設備行192: 失敗項目ATPG_test, 使用Bin值485
2025-07-29 08:51:58,578 - INFO - 設備行193: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,587 - INFO - 設備行195: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 08:51:58,592 - INFO - 設備行196: 失敗項目IOC_code10, 使用Bin值183
2025-07-29 08:51:58,596 - INFO - 設備行197: 失敗項目CR_CH2, 使用Bin值65
2025-07-29 08:51:58,611 - INFO - 設備行200: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,616 - INFO - 設備行201: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,630 - INFO - 設備行204: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,635 - INFO - 設備行205: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,639 - INFO - 設備行206: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,644 - INFO - 設備行207: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,648 - INFO - 設備行208: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,653 - INFO - 設備行209: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-29 08:51:58,658 - INFO - 設備行210: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,676 - INFO - 設備行214: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,681 - INFO - 設備行215: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,690 - INFO - 設備行217: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,698 - INFO - 設備行219: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 08:51:58,725 - INFO - 設備行225: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,730 - INFO - 設備行226: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-29 08:51:58,738 - INFO - 設備行228: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,743 - INFO - 設備行229: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,748 - INFO - 設備行230: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,752 - INFO - 設備行231: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,756 - INFO - 設備行232: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 08:51:58,769 - INFO - 設備行235: 失敗項目READ_0X1D_Aft, 使用Bin值261
2025-07-29 08:51:58,773 - INFO - 設備行236: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 08:51:58,778 - INFO - 設備行237: 失敗項目ISHDN, 使用Bin值353
2025-07-29 08:51:58,787 - INFO - 設備行239: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-29 08:51:58,796 - INFO - 設備行241: 失敗項目ATPG_test, 使用Bin值485
2025-07-29 08:51:58,801 - INFO - 設備行242: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,873 - INFO - 設備行259: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 08:51:58,882 - INFO - 設備行261: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 08:51:58,946 - INFO - 重新計算並更新了264個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 08:51:58,947 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 1.17s
2025-07-29 08:51:58,948 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 16.06s
2025-07-29 08:51:58,948 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 08:51:58,949 - INFO - 應用Device2BinControl處理...
2025-07-29 08:51:58,949 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 08:51:58,950 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 08:51:58,950 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 08:51:58,950 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 08:51:58,951 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 08:51:58,951 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 08:51:58,951 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 08:51:59,051 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 08:51:59,051 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 100.4ms
2025-07-29 08:51:59,052 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 08:51:59,052 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-29 08:51:59,052 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 08:51:59,053 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 08:51:59,053 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 08:51:59,053 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 08:51:59,053 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 08:51:59,054 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 08:51:59,054 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 08:51:59,054 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-29 08:51:59,055 - INFO - 收集原始Bin值: 264個設備
2025-07-29 08:52:00,372 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-29 08:52:00,373 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-29 08:52:00,373 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 08:52:00,374 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 08:52:00,374 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.32s
2025-07-29 08:52:00,374 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 08:52:00,375 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 08:52:00,375 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 08:52:00,375 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 08:52:00,375 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 08:52:00,376 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 08:52:00,376 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 08:52:05,809 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-29 08:52:05,809 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-29 08:52:05,810 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 5.44s
2025-07-29 08:52:05,810 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 6.86s
2025-07-29 08:52:05,810 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 08:52:05,811 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 08:52:05,811 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 08:52:05,811 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 08:52:05,812 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 08:52:05,812 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 08:52:05,812 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 08:52:05,812 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 08:52:05,812 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 08:52:05,813 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 08:52:05,813 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 08:52:05,813 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 08:52:05,813 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 08:52:05,814 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 08:52:05,814 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 08:52:05,814 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 08:52:05,814 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 08:52:05,814 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 08:52:05,815 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 08:52:05,815 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 08:52:05,815 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 08:52:05,909 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 08:52:05,910 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 95.5ms
2025-07-29 08:52:05,910 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 08:52:05,910 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-29 08:52:05,911 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-29 08:52:05,911 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 08:52:05,911 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 08:52:05,912 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 08:52:05,912 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 08:52:05,912 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 08:52:05,913 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 08:52:05,913 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-29 08:52:05,913 - INFO - 收集原始Bin值: 264個設備
2025-07-29 08:52:07,351 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-29 08:52:07,352 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-29 08:52:07,353 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=117, Fail=147, Yield=44.32%
2025-07-29 08:52:07,355 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_gvxwmn2a\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 08:52:07,407 - INFO - VBA 362-420行（修正版）：填充了26個測試項目的Bin數據，無重複
2025-07-29 08:52:07,408 - INFO - 創建Site統計完成: 最大Site號=1，實際有設備的Site數=1
2025-07-29 08:52:07,408 - INFO -   Site 1: 264個設備
2025-07-29 08:52:07,409 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-29 08:52:07,410 - INFO - 填充Site 1統計完成: 264個設備
2025-07-29 08:52:07,410 - INFO - Site統計填充完成: 1個Site
2025-07-29 08:52:07,410 - INFO - 設置 AutoFilter 範圍: A6:G32
2025-07-29 08:52:07,413 - INFO - 按 B 欄由大到小排序了 26 行資料
2025-07-29 08:52:07,414 - INFO - 自動調整D列寬度: 17.6 (基於最大內容長度: 13)
2025-07-29 08:52:07,414 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 08:52:07,414 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 08:52:07,415 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.50s
2025-07-29 08:52:07,415 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 08:52:07,415 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 08:52:07,416 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 08:52:07,416 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 08:52:07,416 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 08:52:07,416 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 08:52:07,417 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 08:52:16,267 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-29 08:52:16,268 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-29 08:52:16,268 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 8.85s
2025-07-29 08:52:16,268 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 10.45s
2025-07-29 08:52:16,269 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 08:52:16,269 - INFO - === 統一處理管道完成 ===
2025-07-29 08:52:33,644 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 08:52:35,707 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時2063.0ms
2025-07-29 08:52:35,707 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-29 08:52:35,711 - INFO - 開始完整轉換...
2025-07-29 08:52:35,711 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_q1wjj6el\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-29 08:52:35,945 - INFO - 成功讀取CSV文件，大小: (7971, 110)
2025-07-29 08:52:35,946 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-29 08:52:35,947 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 08:52:35,947 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-29 08:52:35,948 - INFO - 創建TMT格式工作簿...
2025-07-29 08:52:43,276 - INFO - ⚡ 優化寫入完成: 7971行 × 110列，耗時7326.4ms
2025-07-29 08:52:43,281 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-29 08:52:43,281 - INFO - TMT格式不需要sum分頁
2025-07-29 08:52:43,282 - INFO - TMT工作簿創建完成：僅Data11工作表 7971行，無sum分頁
2025-07-29 08:52:43,283 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 08:52:43,283 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 08:52:43,284 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 08:52:43,284 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 08:52:43,285 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 08:52:43,285 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 08:52:43,286 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 08:52:43,286 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 08:52:43,287 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 08:52:43,288 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 08:52:43,288 - INFO - 🔍 開始統一數據收集...
2025-07-29 08:52:43,289 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 08:52:45,604 - INFO - 收集項目數據: 108個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 08:52:45,605 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 09:37:37,953 - INFO - 收集設備數據: 7959個設備（已排除無測試數據的行）
2025-07-29 09:37:37,954 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 09:37:38,308 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-29 09:37:38,352 - INFO - 收集Site數據: 最大Site號=2，實際Site數=2，7959筆Site資料
2025-07-29 09:37:38,353 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 09:37:38,354 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-29 09:37:38,355 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 09:37:38,356 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-29 09:37:38,357 - INFO - ⏱️ 統一數據收集 執行時間: 2695.07s
2025-07-29 09:37:38,357 - INFO - ✅ 統一數據收集完成: 項目108個, 設備7959個, Site2個
2025-07-29 09:37:38,357 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 2695.07s
2025-07-29 09:37:38,358 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 09:37:38,358 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 09:37:38,359 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 09:37:38,359 - INFO -   - 設置紅色字體標記
2025-07-29 09:37:38,360 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 2.0ms
2025-07-29 09:37:38,360 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 09:37:38,360 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 09:37:38,361 - INFO -   - 填充預設限制值
2025-07-29 09:37:38,361 - INFO -   - 設置紅色字體標記
2025-07-29 09:37:38,362 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 2.0ms
2025-07-29 09:37:38,363 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 09:37:38,363 - INFO -   - 驗證數據完整性
2025-07-29 09:37:38,363 - INFO -   - 準備統一數據摘要
2025-07-29 09:37:38,364 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 09:37:38,364 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 09:37:38,364 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 09:37:38,494 - INFO - 設備行105: 失敗項目I_PS2_typ, 使用Bin值36
2025-07-29 09:37:38,752 - INFO - 設備行290: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:38,775 - INFO - 設備行306: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:38,787 - INFO - 設備行314: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:38,802 - INFO - 設備行324: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:38,899 - INFO - 設備行392: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,266 - INFO - 設備行655: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:39,362 - INFO - 設備行723: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:39,429 - INFO - 設備行771: 失敗項目IQ_max, 使用Bin值31
2025-07-29 09:37:39,478 - INFO - 設備行806: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,490 - INFO - 設備行814: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:39,502 - INFO - 設備行822: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,520 - INFO - 設備行834: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,547 - INFO - 設備行853: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:37:39,557 - INFO - 設備行860: 失敗項目PS2_OVP_R, 使用Bin值97
2025-07-29 09:37:39,569 - INFO - 設備行868: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:39,592 - INFO - 設備行883: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,607 - INFO - 設備行893: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:39,662 - INFO - 設備行931: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,664 - INFO - 設備行932: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,679 - INFO - 設備行942: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,761 - INFO - 設備行998: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,772 - INFO - 設備行1006: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,792 - INFO - 設備行1020: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,804 - INFO - 設備行1028: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:39,844 - INFO - 設備行1057: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,860 - INFO - 設備行1068: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:39,892 - INFO - 設備行1090: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:40,040 - INFO - 設備行1196: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,057 - INFO - 設備行1208: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,102 - INFO - 設備行1240: 失敗項目IQ_max, 使用Bin值31
2025-07-29 09:37:40,105 - INFO - 設備行1241: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,107 - INFO - 設備行1242: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,155 - INFO - 設備行1276: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,163 - INFO - 設備行1282: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,170 - INFO - 設備行1286: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:40,205 - INFO - 設備行1311: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,207 - INFO - 設備行1312: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,227 - INFO - 設備行1326: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,235 - INFO - 設備行1331: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,237 - INFO - 設備行1332: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,504 - INFO - 設備行1519: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,545 - INFO - 設備行1548: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,616 - INFO - 設備行1598: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,623 - INFO - 設備行1602: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,670 - INFO - 設備行1635: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,672 - INFO - 設備行1636: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,676 - INFO - 設備行1639: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,679 - INFO - 設備行1640: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,685 - INFO - 設備行1644: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:40,719 - INFO - 設備行1668: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,729 - INFO - 設備行1675: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,731 - INFO - 設備行1676: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,734 - INFO - 設備行1678: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:40,743 - INFO - 設備行1684: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,761 - INFO - 設備行1696: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,770 - INFO - 設備行1702: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,801 - INFO - 設備行1724: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,816 - INFO - 設備行1734: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,851 - INFO - 設備行1759: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,853 - INFO - 設備行1760: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,856 - INFO - 設備行1761: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,876 - INFO - 設備行1775: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,878 - INFO - 設備行1776: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,897 - INFO - 設備行1789: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,899 - INFO - 設備行1790: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,910 - INFO - 設備行1798: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,913 - INFO - 設備行1800: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,923 - INFO - 設備行1807: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,932 - INFO - 設備行1813: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,950 - INFO - 設備行1825: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,969 - INFO - 設備行1838: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:40,975 - INFO - 設備行1842: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:40,988 - INFO - 設備行1851: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,017 - INFO - 設備行1871: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,033 - INFO - 設備行1882: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,109 - INFO - 設備行1935: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,115 - INFO - 設備行1939: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,141 - INFO - 設備行1957: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,171 - INFO - 設備行1978: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,173 - INFO - 設備行1979: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,210 - INFO - 設備行2005: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,216 - INFO - 設備行2009: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,261 - INFO - 設備行2040: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,330 - INFO - 設備行2090: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,342 - INFO - 設備行2098: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,351 - INFO - 設備行2104: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,356 - INFO - 設備行2107: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,358 - INFO - 設備行2108: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,364 - INFO - 設備行2112: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,373 - INFO - 設備行2118: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:41,377 - INFO - 設備行2120: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:41,378 - INFO - 設備行2121: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,406 - INFO - 設備行2140: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,423 - INFO - 設備行2152: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:41,425 - INFO - 設備行2153: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,427 - INFO - 設備行2154: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,447 - INFO - 設備行2168: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,449 - INFO - 設備行2169: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,451 - INFO - 設備行2170: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,476 - INFO - 設備行2188: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,494 - INFO - 設備行2200: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,499 - INFO - 設備行2203: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,501 - INFO - 設備行2204: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,502 - INFO - 設備行2205: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,504 - INFO - 設備行2206: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,508 - INFO - 設備行2208: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,516 - INFO - 設備行2214: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,524 - INFO - 設備行2219: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,526 - INFO - 設備行2220: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,535 - INFO - 設備行2226: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,551 - INFO - 設備行2237: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,553 - INFO - 設備行2238: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,562 - INFO - 設備行2244: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,572 - INFO - 設備行2251: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,574 - INFO - 設備行2252: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,576 - INFO - 設備行2253: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,581 - INFO - 設備行2256: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,584 - INFO - 設備行2258: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,586 - INFO - 設備行2259: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,587 - INFO - 設備行2260: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,596 - INFO - 設備行2266: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:41,604 - INFO - 設備行2271: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,607 - INFO - 設備行2273: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,613 - INFO - 設備行2277: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,631 - INFO - 設備行2289: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,636 - INFO - 設備行2292: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,638 - INFO - 設備行2293: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,670 - INFO - 設備行2315: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,688 - INFO - 設備行2327: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,703 - INFO - 設備行2337: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,706 - INFO - 設備行2339: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,709 - INFO - 設備行2341: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,715 - INFO - 設備行2345: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,743 - INFO - 設備行2364: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,790 - INFO - 設備行2397: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,801 - INFO - 設備行2405: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,816 - INFO - 設備行2413: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,853 - INFO - 設備行2439: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,890 - INFO - 設備行2465: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,895 - INFO - 設備行2468: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:41,926 - INFO - 設備行2489: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,931 - INFO - 設備行2493: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,943 - INFO - 設備行2501: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,970 - INFO - 設備行2520: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,972 - INFO - 設備行2521: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,979 - INFO - 設備行2525: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,991 - INFO - 設備行2533: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,993 - INFO - 設備行2534: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:41,995 - INFO - 設備行2535: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,004 - INFO - 設備行2541: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,008 - INFO - 設備行2543: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,025 - INFO - 設備行2555: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,028 - INFO - 設備行2557: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,034 - INFO - 設備行2561: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,050 - INFO - 設備行2572: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,053 - INFO - 設備行2573: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,056 - INFO - 設備行2575: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,070 - INFO - 設備行2584: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,072 - INFO - 設備行2585: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,078 - INFO - 設備行2589: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:42,080 - INFO - 設備行2590: 失敗項目PS1OUT_iil, 使用Bin值17
2025-07-29 09:37:42,082 - INFO - 設備行2591: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,094 - INFO - 設備行2599: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,096 - INFO - 設備行2600: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,098 - INFO - 設備行2601: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,105 - INFO - 設備行2606: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,107 - INFO - 設備行2607: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,112 - INFO - 設備行2610: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,114 - INFO - 設備行2611: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,126 - INFO - 設備行2619: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,129 - INFO - 設備行2621: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,138 - INFO - 設備行2627: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,140 - INFO - 設備行2628: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,142 - INFO - 設備行2629: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,151 - INFO - 設備行2635: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,157 - INFO - 設備行2639: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,159 - INFO - 設備行2640: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,161 - INFO - 設備行2641: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,167 - INFO - 設備行2645: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,173 - INFO - 設備行2649: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,176 - INFO - 設備行2651: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,188 - INFO - 設備行2659: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,191 - INFO - 設備行2661: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,196 - INFO - 設備行2664: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,198 - INFO - 設備行2665: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,207 - INFO - 設備行2671: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,211 - INFO - 設備行2673: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,215 - INFO - 設備行2676: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,217 - INFO - 設備行2677: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,237 - INFO - 設備行2691: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,242 - INFO - 設備行2694: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,244 - INFO - 設備行2695: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,246 - INFO - 設備行2696: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,248 - INFO - 設備行2697: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,257 - INFO - 設備行2703: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,268 - INFO - 設備行2710: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,270 - INFO - 設備行2711: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,277 - INFO - 設備行2715: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,283 - INFO - 設備行2719: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,292 - INFO - 設備行2725: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,297 - INFO - 設備行2728: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,300 - INFO - 設備行2729: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,307 - INFO - 設備行2734: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,309 - INFO - 設備行2735: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,314 - INFO - 設備行2738: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,316 - INFO - 設備行2739: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,326 - INFO - 設備行2746: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,328 - INFO - 設備行2747: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,330 - INFO - 設備行2748: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,347 - INFO - 設備行2760: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,353 - INFO - 設備行2764: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,365 - INFO - 設備行2772: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,397 - INFO - 設備行2794: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:42,403 - INFO - 設備行2798: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,445 - INFO - 設備行2828: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,450 - INFO - 設備行2831: 失敗項目PS1OUT_iil, 使用Bin值17
2025-07-29 09:37:42,470 - INFO - 設備行2844: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,473 - INFO - 設備行2846: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,479 - INFO - 設備行2850: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,481 - INFO - 設備行2851: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:42,580 - INFO - 設備行2920: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,606 - INFO - 設備行2938: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,626 - INFO - 設備行2952: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,643 - INFO - 設備行2964: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:42,691 - INFO - 設備行2997: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,694 - INFO - 設備行2999: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,705 - INFO - 設備行3006: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,707 - INFO - 設備行3007: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,710 - INFO - 設備行3009: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,714 - INFO - 設備行3012: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,716 - INFO - 設備行3013: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,718 - INFO - 設備行3014: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,720 - INFO - 設備行3015: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,733 - INFO - 設備行3024: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,735 - INFO - 設備行3025: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,738 - INFO - 設備行3027: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,747 - INFO - 設備行3033: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,753 - INFO - 設備行3037: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,759 - INFO - 設備行3041: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,763 - INFO - 設備行3044: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,765 - INFO - 設備行3045: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,772 - INFO - 設備行3049: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,778 - INFO - 設備行3053: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,780 - INFO - 設備行3054: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,782 - INFO - 設備行3055: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,796 - INFO - 設備行3065: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,802 - INFO - 設備行3069: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,807 - INFO - 設備行3072: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,809 - INFO - 設備行3073: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,823 - INFO - 設備行3083: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,825 - INFO - 設備行3084: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,827 - INFO - 設備行3085: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,830 - INFO - 設備行3087: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,832 - INFO - 設備行3088: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,834 - INFO - 設備行3089: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,848 - INFO - 設備行3098: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,850 - INFO - 設備行3099: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,853 - INFO - 設備行3101: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,855 - INFO - 設備行3102: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,856 - INFO - 設備行3103: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,860 - INFO - 設備行3105: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,867 - INFO - 設備行3110: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,869 - INFO - 設備行3111: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,872 - INFO - 設備行3113: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,876 - INFO - 設備行3115: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,885 - INFO - 設備行3121: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,888 - INFO - 設備行3123: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,891 - INFO - 設備行3125: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,902 - INFO - 設備行3132: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,904 - INFO - 設備行3133: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,905 - INFO - 設備行3134: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,907 - INFO - 設備行3135: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,910 - INFO - 設備行3137: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,914 - INFO - 設備行3139: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,928 - INFO - 設備行3149: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,947 - INFO - 設備行3162: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,949 - INFO - 設備行3163: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,952 - INFO - 設備行3165: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,954 - INFO - 設備行3166: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,956 - INFO - 設備行3167: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,962 - INFO - 設備行3171: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,965 - INFO - 設備行3173: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,969 - INFO - 設備行3175: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,973 - INFO - 設備行3178: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:42,978 - INFO - 設備行3181: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:42,983 - INFO - 設備行3184: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,985 - INFO - 設備行3185: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,990 - INFO - 設備行3188: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,991 - INFO - 設備行3189: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:42,995 - INFO - 設備行3191: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,001 - INFO - 設備行3195: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,003 - INFO - 設備行3196: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,005 - INFO - 設備行3197: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,011 - INFO - 設備行3201: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,015 - INFO - 設備行3204: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,017 - INFO - 設備行3205: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,032 - INFO - 設備行3215: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,034 - INFO - 設備行3216: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,036 - INFO - 設備行3217: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,039 - INFO - 設備行3219: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,045 - INFO - 設備行3223: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,047 - INFO - 設備行3224: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,049 - INFO - 設備行3225: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,051 - INFO - 設備行3226: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,053 - INFO - 設備行3227: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,056 - INFO - 設備行3229: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,061 - INFO - 設備行3232: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,062 - INFO - 設備行3233: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,066 - INFO - 設備行3235: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,089 - INFO - 設備行3251: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,099 - INFO - 設備行3258: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,101 - INFO - 設備行3259: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,103 - INFO - 設備行3260: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,105 - INFO - 設備行3261: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,108 - INFO - 設備行3263: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,120 - INFO - 設備行3271: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,126 - INFO - 設備行3275: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,134 - INFO - 設備行3280: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,136 - INFO - 設備行3281: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,171 - INFO - 設備行3306: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,173 - INFO - 設備行3307: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,179 - INFO - 設備行3311: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,185 - INFO - 設備行3315: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,191 - INFO - 設備行3319: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,195 - INFO - 設備行3321: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,204 - INFO - 設備行3327: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:43,206 - INFO - 設備行3328: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,208 - INFO - 設備行3329: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,214 - INFO - 設備行3333: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,220 - INFO - 設備行3337: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,232 - INFO - 設備行3345: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,242 - INFO - 設備行3352: 失敗項目PS2_OVP_R, 使用Bin值97
2025-07-29 09:37:43,246 - INFO - 設備行3355: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,248 - INFO - 設備行3356: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,250 - INFO - 設備行3357: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,264 - INFO - 設備行3367: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:43,273 - INFO - 設備行3373: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,279 - INFO - 設備行3377: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,283 - INFO - 設備行3380: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,285 - INFO - 設備行3381: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,291 - INFO - 設備行3385: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,314 - INFO - 設備行3401: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,320 - INFO - 設備行3405: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,323 - INFO - 設備行3407: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,326 - INFO - 設備行3409: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:43,357 - INFO - 設備行3431: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,360 - INFO - 設備行3433: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,397 - INFO - 設備行3459: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,452 - INFO - 設備行3498: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,454 - INFO - 設備行3499: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,458 - INFO - 設備行3501: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,496 - INFO - 設備行3528: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,498 - INFO - 設備行3529: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,501 - INFO - 設備行3531: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,503 - INFO - 設備行3532: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,505 - INFO - 設備行3533: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,512 - INFO - 設備行3538: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,513 - INFO - 設備行3539: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,522 - INFO - 設備行3544: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,523 - INFO - 設備行3545: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,539 - INFO - 設備行3556: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,541 - INFO - 設備行3557: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,550 - INFO - 設備行3563: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,552 - INFO - 設備行3564: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,554 - INFO - 設備行3565: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,559 - INFO - 設備行3568: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,561 - INFO - 設備行3569: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,566 - INFO - 設備行3572: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,568 - INFO - 設備行3573: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,570 - INFO - 設備行3574: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,572 - INFO - 設備行3575: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,574 - INFO - 設備行3576: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,576 - INFO - 設備行3577: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,579 - INFO - 設備行3579: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,582 - INFO - 設備行3581: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,587 - INFO - 設備行3584: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,589 - INFO - 設備行3585: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,592 - INFO - 設備行3587: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,597 - INFO - 設備行3590: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,599 - INFO - 設備行3591: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,605 - INFO - 設備行3595: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,612 - INFO - 設備行3600: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,614 - INFO - 設備行3601: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,623 - INFO - 設備行3607: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,624 - INFO - 設備行3608: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,626 - INFO - 設備行3609: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,631 - INFO - 設備行3612: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,632 - INFO - 設備行3613: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,644 - INFO - 設備行3621: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,647 - INFO - 設備行3623: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,651 - INFO - 設備行3626: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,653 - INFO - 設備行3627: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,659 - INFO - 設備行3631: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,665 - INFO - 設備行3635: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,671 - INFO - 設備行3639: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,673 - INFO - 設備行3640: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,675 - INFO - 設備行3641: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,692 - INFO - 設備行3653: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,694 - INFO - 設備行3654: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,696 - INFO - 設備行3655: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,701 - INFO - 設備行3658: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,703 - INFO - 設備行3659: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,706 - INFO - 設備行3661: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,710 - INFO - 設備行3663: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,720 - INFO - 設備行3670: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,722 - INFO - 設備行3671: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,730 - INFO - 設備行3676: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,731 - INFO - 設備行3677: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,747 - INFO - 設備行3688: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,749 - INFO - 設備行3689: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,751 - INFO - 設備行3690: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,753 - INFO - 設備行3691: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,757 - INFO - 設備行3693: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,763 - INFO - 設備行3697: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,765 - INFO - 設備行3698: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,766 - INFO - 設備行3699: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,774 - INFO - 設備行3704: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,775 - INFO - 設備行3705: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,778 - INFO - 設備行3707: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,782 - INFO - 設備行3709: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,786 - INFO - 設備行3711: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:37:43,788 - INFO - 設備行3712: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,790 - INFO - 設備行3713: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,796 - INFO - 設備行3717: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,799 - INFO - 設備行3719: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,801 - INFO - 設備行3720: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,803 - INFO - 設備行3721: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,805 - INFO - 設備行3722: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,807 - INFO - 設備行3723: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,816 - INFO - 設備行3729: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,823 - INFO - 設備行3733: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:43,824 - INFO - 設備行3734: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:43,826 - INFO - 設備行3735: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,831 - INFO - 設備行3738: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,832 - INFO - 設備行3739: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,834 - INFO - 設備行3740: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,836 - INFO - 設備行3741: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,839 - INFO - 設備行3743: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,842 - INFO - 設備行3745: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,846 - INFO - 設備行3747: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,860 - INFO - 設備行3757: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,864 - INFO - 設備行3759: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,874 - INFO - 設備行3766: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,876 - INFO - 設備行3767: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,878 - INFO - 設備行3768: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,879 - INFO - 設備行3769: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,881 - INFO - 設備行3770: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,883 - INFO - 設備行3771: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,886 - INFO - 設備行3773: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,904 - INFO - 設備行3786: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,906 - INFO - 設備行3787: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,911 - INFO - 設備行3790: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,913 - INFO - 設備行3791: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,921 - INFO - 設備行3797: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,925 - INFO - 設備行3799: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:43,928 - INFO - 設備行3801: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,931 - INFO - 設備行3803: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,951 - INFO - 設備行3817: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,955 - INFO - 設備行3820: 失敗項目I_PS1_min, 使用Bin值32
2025-07-29 09:37:43,957 - INFO - 設備行3821: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,961 - INFO - 設備行3823: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,975 - INFO - 設備行3833: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,983 - INFO - 設備行3838: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,985 - INFO - 設備行3839: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:43,996 - INFO - 設備行3847: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,010 - INFO - 設備行3857: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:44,014 - INFO - 設備行3859: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,028 - INFO - 設備行3869: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,054 - INFO - 設備行3887: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,060 - INFO - 設備行3891: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,082 - INFO - 設備行3906: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,084 - INFO - 設備行3907: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,088 - INFO - 設備行3909: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,102 - INFO - 設備行3919: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,104 - INFO - 設備行3920: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,106 - INFO - 設備行3921: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,126 - INFO - 設備行3935: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,176 - INFO - 設備行3971: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,188 - INFO - 設備行3979: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,194 - INFO - 設備行3983: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,196 - INFO - 設備行3984: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,202 - INFO - 設備行3988: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,210 - INFO - 設備行3993: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,212 - INFO - 設備行3994: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,216 - INFO - 設備行3996: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,225 - INFO - 設備行4002: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,227 - INFO - 設備行4003: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,229 - INFO - 設備行4004: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,238 - INFO - 設備行4010: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,245 - INFO - 設備行4015: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,247 - INFO - 設備行4016: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,251 - INFO - 設備行4019: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,253 - INFO - 設備行4020: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,255 - INFO - 設備行4021: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,257 - INFO - 設備行4022: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,270 - INFO - 設備行4031: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,272 - INFO - 設備行4032: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,276 - INFO - 設備行4035: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,278 - INFO - 設備行4036: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,293 - INFO - 設備行4046: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,312 - INFO - 設備行4060: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,353 - INFO - 設備行4088: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,384 - INFO - 設備行4110: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,386 - INFO - 設備行4111: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,388 - INFO - 設備行4112: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,399 - INFO - 設備行4119: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,401 - INFO - 設備行4120: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,438 - INFO - 設備行4147: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,440 - INFO - 設備行4148: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,447 - INFO - 設備行4152: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,506 - INFO - 設備行4194: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,513 - INFO - 設備行4199: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,515 - INFO - 設備行4200: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,528 - INFO - 設備行4209: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,530 - INFO - 設備行4210: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,532 - INFO - 設備行4211: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,534 - INFO - 設備行4212: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,540 - INFO - 設備行4216: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,545 - INFO - 設備行4219: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,552 - INFO - 設備行4224: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,554 - INFO - 設備行4225: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,556 - INFO - 設備行4226: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,558 - INFO - 設備行4227: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,564 - INFO - 設備行4231: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,584 - INFO - 設備行4245: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,595 - INFO - 設備行4252: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,597 - INFO - 設備行4253: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,602 - INFO - 設備行4256: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,604 - INFO - 設備行4257: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,607 - INFO - 設備行4259: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,612 - INFO - 設備行4262: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,614 - INFO - 設備行4263: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,624 - INFO - 設備行4270: 失敗項目SDA_iil, 使用Bin值19
2025-07-29 09:37:44,646 - INFO - 設備行4285: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,671 - INFO - 設備行4303: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,687 - INFO - 設備行4314: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,689 - INFO - 設備行4315: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,703 - INFO - 設備行4324: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,704 - INFO - 設備行4325: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,706 - INFO - 設備行4326: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,708 - INFO - 設備行4327: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,741 - INFO - 設備行4350: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,743 - INFO - 設備行4351: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,752 - INFO - 設備行4357: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,761 - INFO - 設備行4363: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,808 - INFO - 設備行4396: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:44,816 - INFO - 設備行4401: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,835 - INFO - 設備行4414: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,851 - INFO - 設備行4425: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,893 - INFO - 設備行4455: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,913 - INFO - 設備行4469: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,917 - INFO - 設備行4471: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,919 - INFO - 設備行4472: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,921 - INFO - 設備行4473: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,933 - INFO - 設備行4481: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:44,940 - INFO - 設備行4486: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:44,945 - INFO - 設備行4489: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,949 - INFO - 設備行4491: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,958 - INFO - 設備行4497: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,964 - INFO - 設備行4501: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,996 - INFO - 設備行4524: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:44,998 - INFO - 設備行4525: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,002 - INFO - 設備行4527: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,009 - INFO - 設備行4532: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,011 - INFO - 設備行4533: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,016 - INFO - 設備行4536: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,016 - INFO - 設備行4537: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,018 - INFO - 設備行4538: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,020 - INFO - 設備行4539: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,029 - INFO - 設備行4545: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:45,040 - INFO - 設備行4552: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,041 - INFO - 設備行4553: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,049 - INFO - 設備行4558: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,051 - INFO - 設備行4559: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,058 - INFO - 設備行4564: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,060 - INFO - 設備行4565: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,066 - INFO - 設備行4569: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,072 - INFO - 設備行4573: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,074 - INFO - 設備行4574: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,076 - INFO - 設備行4575: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,088 - INFO - 設備行4583: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,100 - INFO - 設備行4591: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,105 - INFO - 設備行4594: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,107 - INFO - 設備行4595: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,107 - INFO - 設備行4596: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,109 - INFO - 設備行4597: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,115 - INFO - 設備行4600: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,115 - INFO - 設備行4601: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,123 - INFO - 設備行4606: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,125 - INFO - 設備行4607: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,128 - INFO - 設備行4609: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,133 - INFO - 設備行4612: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:45,135 - INFO - 設備行4613: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,148 - INFO - 設備行4621: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,151 - INFO - 設備行4624: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:45,153 - INFO - 設備行4625: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,155 - INFO - 設備行4626: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,157 - INFO - 設備行4627: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,164 - INFO - 設備行4632: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,166 - INFO - 設備行4633: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,170 - INFO - 設備行4635: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,180 - INFO - 設備行4642: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,182 - INFO - 設備行4643: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,184 - INFO - 設備行4644: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,185 - INFO - 設備行4645: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,203 - INFO - 設備行4657: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,208 - INFO - 設備行4660: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,210 - INFO - 設備行4661: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,217 - INFO - 設備行4666: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,219 - INFO - 設備行4667: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,233 - INFO - 設備行4676: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,235 - INFO - 設備行4677: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,241 - INFO - 設備行4681: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,243 - INFO - 設備行4682: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,245 - INFO - 設備行4683: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,249 - INFO - 設備行4685: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,251 - INFO - 設備行4686: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,253 - INFO - 設備行4687: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,258 - INFO - 設備行4690: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,260 - INFO - 設備行4691: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,264 - INFO - 設備行4694: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,266 - INFO - 設備行4695: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,268 - INFO - 設備行4696: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,271 - INFO - 設備行4697: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,278 - INFO - 設備行4702: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,279 - INFO - 設備行4703: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,295 - INFO - 設備行4713: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,298 - INFO - 設備行4715: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,300 - INFO - 設備行4716: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,301 - INFO - 設備行4717: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,304 - INFO - 設備行4719: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,306 - INFO - 設備行4720: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,308 - INFO - 設備行4721: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,319 - INFO - 設備行4729: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,327 - INFO - 設備行4734: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,328 - INFO - 設備行4735: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,333 - INFO - 設備行4738: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,335 - INFO - 設備行4739: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,337 - INFO - 設備行4740: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,338 - INFO - 設備行4741: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,344 - INFO - 設備行4745: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,349 - INFO - 設備行4748: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,351 - INFO - 設備行4749: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,382 - INFO - 設備行4770: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,384 - INFO - 設備行4771: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,390 - INFO - 設備行4775: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,412 - INFO - 設備行4790: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,412 - INFO - 設備行4791: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,418 - INFO - 設備行4794: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,420 - INFO - 設備行4795: 失敗項目I_PS1_min, 使用Bin值32
2025-07-29 09:37:45,422 - INFO - 設備行4797: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,438 - INFO - 設備行4807: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,452 - INFO - 設備行4817: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,457 - INFO - 設備行4820: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,457 - INFO - 設備行4821: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,462 - INFO - 設備行4823: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,470 - INFO - 設備行4829: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,485 - INFO - 設備行4839: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,486 - INFO - 設備行4840: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,488 - INFO - 設備行4841: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,523 - INFO - 設備行4865: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,532 - INFO - 設備行4871: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,558 - INFO - 設備行4889: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,584 - INFO - 設備行4907: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,601 - INFO - 設備行4919: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:45,610 - INFO - 設備行4925: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,627 - INFO - 設備行4937: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,629 - INFO - 設備行4938: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,629 - INFO - 設備行4939: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,631 - INFO - 設備行4940: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,635 - INFO - 設備行4942: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,643 - INFO - 設備行4948: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,647 - INFO - 設備行4950: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,655 - INFO - 設備行4956: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,660 - INFO - 設備行4959: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,662 - INFO - 設備行4960: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,668 - INFO - 設備行4964: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,671 - INFO - 設備行4966: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,676 - INFO - 設備行4969: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:45,682 - INFO - 設備行4973: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,683 - INFO - 設備行4974: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,688 - INFO - 設備行4977: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,690 - INFO - 設備行4978: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,699 - INFO - 設備行4984: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:45,703 - INFO - 設備行4986: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,720 - INFO - 設備行4998: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,722 - INFO - 設備行4999: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,724 - INFO - 設備行5000: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,733 - INFO - 設備行5006: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,744 - INFO - 設備行5013: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,746 - INFO - 設備行5014: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,749 - INFO - 設備行5016: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,755 - INFO - 設備行5020: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:45,758 - INFO - 設備行5022: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,762 - INFO - 設備行5024: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,765 - INFO - 設備行5026: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,769 - INFO - 設備行5028: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,773 - INFO - 設備行5031: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,775 - INFO - 設備行5032: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,779 - INFO - 設備行5034: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,781 - INFO - 設備行5035: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,783 - INFO - 設備行5036: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,785 - INFO - 設備行5037: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,787 - INFO - 設備行5038: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,790 - INFO - 設備行5040: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,793 - INFO - 設備行5042: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,795 - INFO - 設備行5043: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,797 - INFO - 設備行5044: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,802 - INFO - 設備行5047: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,804 - INFO - 設備行5048: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,806 - INFO - 設備行5049: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,808 - INFO - 設備行5050: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,811 - INFO - 設備行5052: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,817 - INFO - 設備行5056: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,819 - INFO - 設備行5057: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,821 - INFO - 設備行5058: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,823 - INFO - 設備行5059: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,824 - INFO - 設備行5060: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,827 - INFO - 設備行5062: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,833 - INFO - 設備行5066: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,836 - INFO - 設備行5068: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,847 - INFO - 設備行5075: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,849 - INFO - 設備行5076: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,860 - INFO - 設備行5084: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,876 - INFO - 設備行5095: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,878 - INFO - 設備行5096: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,882 - INFO - 設備行5098: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:37:45,885 - INFO - 設備行5100: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,895 - INFO - 設備行5107: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,897 - INFO - 設備行5108: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,899 - INFO - 設備行5109: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,901 - INFO - 設備行5110: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,904 - INFO - 設備行5112: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,907 - INFO - 設備行5114: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,921 - INFO - 設備行5124: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,923 - INFO - 設備行5125: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,925 - INFO - 設備行5126: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,934 - INFO - 設備行5132: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,936 - INFO - 設備行5133: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,938 - INFO - 設備行5134: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,944 - INFO - 設備行5138: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,946 - INFO - 設備行5139: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,948 - INFO - 設備行5140: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,952 - INFO - 設備行5143: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,954 - INFO - 設備行5144: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,963 - INFO - 設備行5150: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,974 - INFO - 設備行5157: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,975 - INFO - 設備行5158: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,977 - INFO - 設備行5159: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,979 - INFO - 設備行5160: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,981 - INFO - 設備行5161: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,983 - INFO - 設備行5162: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,984 - INFO - 設備行5163: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,986 - INFO - 設備行5164: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,989 - INFO - 設備行5166: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,994 - INFO - 設備行5169: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,995 - INFO - 設備行5170: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,997 - INFO - 設備行5171: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:45,999 - INFO - 設備行5172: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,002 - INFO - 設備行5174: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,005 - INFO - 設備行5176: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,007 - INFO - 設備行5177: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,009 - INFO - 設備行5178: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,026 - INFO - 設備行5191: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,029 - INFO - 設備行5192: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,033 - INFO - 設備行5195: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,034 - INFO - 設備行5196: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,043 - INFO - 設備行5202: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,045 - INFO - 設備行5203: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,047 - INFO - 設備行5204: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,051 - INFO - 設備行5206: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,061 - INFO - 設備行5213: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,063 - INFO - 設備行5214: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,067 - INFO - 設備行5216: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,073 - INFO - 設備行5220: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,076 - INFO - 設備行5222: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,078 - INFO - 設備行5223: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,080 - INFO - 設備行5224: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,084 - INFO - 設備行5226: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,091 - INFO - 設備行5231: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,094 - INFO - 設備行5232: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,104 - INFO - 設備行5239: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,114 - INFO - 設備行5246: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,118 - INFO - 設備行5248: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,130 - INFO - 設備行5256: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,161 - INFO - 設備行5278: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,182 - INFO - 設備行5293: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,184 - INFO - 設備行5294: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,186 - INFO - 設備行5295: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,188 - INFO - 設備行5296: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,212 - INFO - 設備行5313: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,214 - INFO - 設備行5314: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,218 - INFO - 設備行5316: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,232 - INFO - 設備行5326: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,258 - INFO - 設備行5344: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,283 - INFO - 設備行5362: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,285 - INFO - 設備行5363: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,325 - INFO - 設備行5391: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,328 - INFO - 設備行5393: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,337 - INFO - 設備行5399: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,340 - INFO - 設備行5401: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,346 - INFO - 設備行5405: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,350 - INFO - 設備行5407: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,352 - INFO - 設備行5408: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,354 - INFO - 設備行5409: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,357 - INFO - 設備行5411: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,359 - INFO - 設備行5412: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,361 - INFO - 設備行5413: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,363 - INFO - 設備行5414: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,365 - INFO - 設備行5415: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,367 - INFO - 設備行5416: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,369 - INFO - 設備行5417: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,371 - INFO - 設備行5418: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,373 - INFO - 設備行5419: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,375 - INFO - 設備行5420: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,377 - INFO - 設備行5421: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,380 - INFO - 設備行5423: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,384 - INFO - 設備行5426: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,386 - INFO - 設備行5427: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,415 - INFO - 設備行5447: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,418 - INFO - 設備行5449: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,463 - INFO - 設備行5481: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,467 - INFO - 設備行5483: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,469 - INFO - 設備行5484: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,471 - INFO - 設備行5485: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,472 - INFO - 設備行5486: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,474 - INFO - 設備行5487: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,489 - INFO - 設備行5497: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,492 - INFO - 設備行5499: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,494 - INFO - 設備行5500: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,496 - INFO - 設備行5501: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,517 - INFO - 設備行5515: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,554 - INFO - 設備行5541: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,570 - INFO - 設備行5552: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,572 - INFO - 設備行5553: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,575 - INFO - 設備行5555: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,581 - INFO - 設備行5559: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,585 - INFO - 設備行5561: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,587 - INFO - 設備行5562: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,608 - INFO - 設備行5577: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,620 - INFO - 設備行5585: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,625 - INFO - 設備行5588: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,630 - INFO - 設備行5591: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,643 - INFO - 設備行5600: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,645 - INFO - 設備行5601: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,674 - INFO - 設備行5621: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,676 - INFO - 設備行5622: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,678 - INFO - 設備行5623: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,718 - INFO - 設備行5651: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,724 - INFO - 設備行5655: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:46,726 - INFO - 設備行5656: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,749 - INFO - 設備行5672: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,780 - INFO - 設備行5694: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:46,800 - INFO - 設備行5708: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,808 - INFO - 設備行5713: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,822 - INFO - 設備行5723: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,888 - INFO - 設備行5769: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:46,936 - INFO - 設備行5802: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,941 - INFO - 設備行5806: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,944 - INFO - 設備行5807: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,946 - INFO - 設備行5808: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,954 - INFO - 設備行5814: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,965 - INFO - 設備行5821: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,967 - INFO - 設備行5822: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:46,976 - INFO - 設備行5828: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,001 - INFO - 設備行5845: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:47,003 - INFO - 設備行5846: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,009 - INFO - 設備行5850: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,013 - INFO - 設備行5852: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,016 - INFO - 設備行5854: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:47,030 - INFO - 設備行5864: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,037 - INFO - 設備行5868: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,038 - INFO - 設備行5869: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,041 - INFO - 設備行5870: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,053 - INFO - 設備行5878: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,055 - INFO - 設備行5879: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,057 - INFO - 設備行5880: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,066 - INFO - 設備行5886: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,075 - INFO - 設備行5892: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,082 - INFO - 設備行5897: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,084 - INFO - 設備行5898: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,091 - INFO - 設備行5903: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:47,104 - INFO - 設備行5912: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,108 - INFO - 設備行5914: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,111 - INFO - 設備行5916: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,128 - INFO - 設備行5928: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,148 - INFO - 設備行5942: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,154 - INFO - 設備行5946: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,163 - INFO - 設備行5952: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,165 - INFO - 設備行5953: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,167 - INFO - 設備行5954: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,170 - INFO - 設備行5956: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,174 - INFO - 設備行5958: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,176 - INFO - 設備行5959: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,178 - INFO - 設備行5960: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,190 - INFO - 設備行5968: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,203 - INFO - 設備行5977: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,205 - INFO - 設備行5978: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,207 - INFO - 設備行5979: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,209 - INFO - 設備行5980: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,216 - INFO - 設備行5985: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,218 - INFO - 設備行5986: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,241 - INFO - 設備行6002: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,252 - INFO - 設備行6009: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,253 - INFO - 設備行6010: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,257 - INFO - 設備行6012: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,287 - INFO - 設備行6034: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,319 - INFO - 設備行6056: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:47,321 - INFO - 設備行6057: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,323 - INFO - 設備行6058: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,357 - INFO - 設備行6082: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,360 - INFO - 設備行6084: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,379 - INFO - 設備行6097: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:47,381 - INFO - 設備行6098: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,413 - INFO - 設備行6120: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,418 - INFO - 設備行6123: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,420 - INFO - 設備行6124: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,422 - INFO - 設備行6125: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,423 - INFO - 設備行6126: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,427 - INFO - 設備行6128: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,436 - INFO - 設備行6134: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,450 - INFO - 設備行6144: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:47,492 - INFO - 設備行6173: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,498 - INFO - 設備行6177: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,505 - INFO - 設備行6181: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,511 - INFO - 設備行6185: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,513 - INFO - 設備行6186: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,515 - INFO - 設備行6187: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,518 - INFO - 設備行6189: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:47,520 - INFO - 設備行6190: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,522 - INFO - 設備行6191: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,524 - INFO - 設備行6192: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,526 - INFO - 設備行6193: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,529 - INFO - 設備行6195: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,539 - INFO - 設備行6201: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:47,542 - INFO - 設備行6203: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,546 - INFO - 設備行6206: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,548 - INFO - 設備行6207: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,554 - INFO - 設備行6211: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,570 - INFO - 設備行6222: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,572 - INFO - 設備行6223: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,574 - INFO - 設備行6224: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,576 - INFO - 設備行6225: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,577 - INFO - 設備行6226: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,579 - INFO - 設備行6227: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,582 - INFO - 設備行6229: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,591 - INFO - 設備行6235: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:47,600 - INFO - 設備行6241: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,606 - INFO - 設備行6245: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,610 - INFO - 設備行6247: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,624 - INFO - 設備行6257: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,630 - INFO - 設備行6261: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,634 - INFO - 設備行6263: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,649 - INFO - 設備行6273: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,652 - INFO - 設備行6275: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,656 - INFO - 設備行6278: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,658 - INFO - 設備行6279: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,664 - INFO - 設備行6283: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,668 - INFO - 設備行6285: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,670 - INFO - 設備行6286: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,671 - INFO - 設備行6287: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,680 - INFO - 設備行6293: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,685 - INFO - 設備行6296: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,687 - INFO - 設備行6297: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,693 - INFO - 設備行6301: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,697 - INFO - 設備行6303: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:47,702 - INFO - 設備行6306: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,704 - INFO - 設備行6307: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,706 - INFO - 設備行6308: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,708 - INFO - 設備行6309: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,710 - INFO - 設備行6310: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,712 - INFO - 設備行6311: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,729 - INFO - 設備行6323: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,731 - INFO - 設備行6324: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,733 - INFO - 設備行6325: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,739 - INFO - 設備行6329: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,756 - INFO - 設備行6340: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,758 - INFO - 設備行6341: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,760 - INFO - 設備行6342: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,762 - INFO - 設備行6343: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,768 - INFO - 設備行6347: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,770 - INFO - 設備行6348: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,772 - INFO - 設備行6349: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,773 - INFO - 設備行6350: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,775 - INFO - 設備行6351: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,779 - INFO - 設備行6353: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,785 - INFO - 設備行6357: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,796 - INFO - 設備行6365: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,798 - INFO - 設備行6366: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,800 - INFO - 設備行6367: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,809 - INFO - 設備行6373: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,815 - INFO - 設備行6376: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,817 - INFO - 設備行6377: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,838 - INFO - 設備行6391: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,844 - INFO - 設備行6395: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,850 - INFO - 設備行6399: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,859 - INFO - 設備行6405: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:47,871 - INFO - 設備行6413: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,886 - INFO - 設備行6423: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,912 - INFO - 設備行6441: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,915 - INFO - 設備行6443: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,930 - INFO - 設備行6453: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:47,954 - INFO - 設備行6470: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:47,959 - INFO - 設備行6473: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,996 - INFO - 設備行6499: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:47,999 - INFO - 設備行6501: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,002 - INFO - 設備行6503: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,006 - INFO - 設備行6505: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,015 - INFO - 設備行6511: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,023 - INFO - 設備行6516: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,025 - INFO - 設備行6517: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,028 - INFO - 設備行6519: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,031 - INFO - 設備行6521: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:48,033 - INFO - 設備行6522: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,035 - INFO - 設備行6523: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:48,039 - INFO - 設備行6525: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,041 - INFO - 設備行6526: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,043 - INFO - 設備行6527: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,051 - INFO - 設備行6533: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,060 - INFO - 設備行6539: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,064 - INFO - 設備行6541: 失敗項目SDA_iih, 使用Bin值24
2025-07-29 09:37:48,066 - INFO - 設備行6542: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,068 - INFO - 設備行6543: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,073 - INFO - 設備行6546: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,075 - INFO - 設備行6547: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,083 - INFO - 設備行6552: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,085 - INFO - 設備行6553: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,090 - INFO - 設備行6556: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,092 - INFO - 設備行6557: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,095 - INFO - 設備行6559: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,101 - INFO - 設備行6563: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:48,105 - INFO - 設備行6565: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,108 - INFO - 設備行6567: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,114 - INFO - 設備行6571: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,120 - INFO - 設備行6575: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,124 - INFO - 設備行6577: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,138 - INFO - 設備行6587: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,145 - INFO - 設備行6591: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,154 - INFO - 設備行6597: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,163 - INFO - 設備行6603: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,167 - INFO - 設備行6606: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,169 - INFO - 設備行6607: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,174 - INFO - 設備行6610: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,175 - INFO - 設備行6611: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,177 - INFO - 設備行6612: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,179 - INFO - 設備行6613: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,181 - INFO - 設備行6614: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,183 - INFO - 設備行6615: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,192 - INFO - 設備行6621: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,195 - INFO - 設備行6623: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,197 - INFO - 設備行6624: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,198 - INFO - 設備行6625: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,200 - INFO - 設備行6626: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,202 - INFO - 設備行6627: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,209 - INFO - 設備行6632: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,211 - INFO - 設備行6633: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,213 - INFO - 設備行6634: 失敗項目IQ_typ, 使用Bin值30
2025-07-29 09:37:48,215 - INFO - 設備行6635: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,221 - INFO - 設備行6639: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,231 - INFO - 設備行6646: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,233 - INFO - 設備行6647: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,235 - INFO - 設備行6648: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,237 - INFO - 設備行6649: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,238 - INFO - 設備行6650: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,242 - INFO - 設備行6652: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,258 - INFO - 設備行6663: 失敗項目PS1OUT_iil, 使用Bin值17
2025-07-29 09:37:48,274 - INFO - 設備行6674: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,277 - INFO - 設備行6676: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,283 - INFO - 設備行6680: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,305 - INFO - 設備行6695: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,307 - INFO - 設備行6696: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,309 - INFO - 設備行6697: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,322 - INFO - 設備行6706: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,354 - INFO - 設備行6728: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,357 - INFO - 設備行6730: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,379 - INFO - 設備行6745: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,381 - INFO - 設備行6746: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,384 - INFO - 設備行6748: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,390 - INFO - 設備行6752: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,394 - INFO - 設備行6755: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:48,395 - INFO - 設備行6756: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,407 - INFO - 設備行6764: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,409 - INFO - 設備行6765: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,412 - INFO - 設備行6767: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,414 - INFO - 設備行6768: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,417 - INFO - 設備行6770: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,438 - INFO - 設備行6784: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,440 - INFO - 設備行6785: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,442 - INFO - 設備行6786: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,446 - INFO - 設備行6788: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,450 - INFO - 設備行6791: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,452 - INFO - 設備行6792: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,461 - INFO - 設備行6798: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,469 - INFO - 設備行6803: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,471 - INFO - 設備行6804: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,474 - INFO - 設備行6806: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,483 - INFO - 設備行6812: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,489 - INFO - 設備行6816: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,493 - INFO - 設備行6819: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:48,495 - INFO - 設備行6820: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,498 - INFO - 設備行6822: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,502 - INFO - 設備行6824: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,515 - INFO - 設備行6833: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,517 - INFO - 設備行6834: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,520 - INFO - 設備行6836: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,525 - INFO - 設備行6839: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:48,526 - INFO - 設備行6840: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,530 - INFO - 設備行6842: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,536 - INFO - 設備行6846: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,540 - INFO - 設備行6849: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,542 - INFO - 設備行6850: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,545 - INFO - 設備行6852: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,547 - INFO - 設備行6853: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,549 - INFO - 設備行6854: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,554 - INFO - 設備行6857: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,555 - INFO - 設備行6858: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,557 - INFO - 設備行6859: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,559 - INFO - 設備行6860: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,561 - INFO - 設備行6861: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,563 - INFO - 設備行6862: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,564 - INFO - 設備行6863: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:48,568 - INFO - 設備行6865: 失敗項目IQ_max, 使用Bin值31
2025-07-29 09:37:48,571 - INFO - 設備行6867: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:48,573 - INFO - 設備行6868: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,574 - INFO - 設備行6869: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,576 - INFO - 設備行6870: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:37:48,581 - INFO - 設備行6873: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,590 - INFO - 設備行6879: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,595 - INFO - 設備行6882: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,597 - INFO - 設備行6883: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,600 - INFO - 設備行6885: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,604 - INFO - 設備行6887: 失敗項目IQ_max, 使用Bin值31
2025-07-29 09:37:48,609 - INFO - 設備行6890: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,619 - INFO - 設備行6897: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,621 - INFO - 設備行6898: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,623 - INFO - 設備行6899: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,634 - INFO - 設備行6906: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:48,640 - INFO - 設備行6910: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,643 - INFO - 設備行6912: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,652 - INFO - 設備行6918: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,667 - INFO - 設備行6928: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,669 - INFO - 設備行6929: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,674 - INFO - 設備行6932: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,676 - INFO - 設備行6933: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,690 - INFO - 設備行6943: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,692 - INFO - 設備行6944: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,694 - INFO - 設備行6945: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,697 - INFO - 設備行6947: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:48,700 - INFO - 設備行6949: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,707 - INFO - 設備行6953: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,708 - INFO - 設備行6954: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,713 - INFO - 設備行6957: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,723 - INFO - 設備行6964: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,736 - INFO - 設備行6973: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,740 - INFO - 設備行6975: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,746 - INFO - 設備行6979: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,747 - INFO - 設備行6980: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,749 - INFO - 設備行6981: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,758 - INFO - 設備行6987: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,764 - INFO - 設備行6991: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,769 - INFO - 設備行6994: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,773 - INFO - 設備行6997: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,802 - INFO - 設備行7017: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:48,804 - INFO - 設備行7018: 失敗項目PS1OUT_iil, 使用Bin值17
2025-07-29 09:37:48,813 - INFO - 設備行7024: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,816 - INFO - 設備行7026: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,830 - INFO - 設備行7036: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,857 - INFO - 設備行7055: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,875 - INFO - 設備行7067: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,876 - INFO - 設備行7068: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,879 - INFO - 設備行7070: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:48,885 - INFO - 設備行7073: 失敗項目PS1OUT_iil, 使用Bin值17
2025-07-29 09:37:48,891 - INFO - 設備行7077: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,896 - INFO - 設備行7080: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,898 - INFO - 設備行7081: 失敗項目PS2_OVP_R, 使用Bin值97
2025-07-29 09:37:48,925 - INFO - 設備行7100: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,930 - INFO - 設備行7103: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,950 - INFO - 設備行7117: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,952 - INFO - 設備行7118: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,958 - INFO - 設備行7122: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:48,986 - INFO - 設備行7141: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,988 - INFO - 設備行7142: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:48,998 - INFO - 設備行7149: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,000 - INFO - 設備行7150: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,003 - INFO - 設備行7152: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,005 - INFO - 設備行7153: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,008 - INFO - 設備行7155: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,010 - INFO - 設備行7156: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,013 - INFO - 設備行7158: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,016 - INFO - 設備行7160: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,025 - INFO - 設備行7166: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,040 - INFO - 設備行7177: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,054 - INFO - 設備行7186: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,064 - INFO - 設備行7193: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,080 - INFO - 設備行7204: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,118 - INFO - 設備行7230: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,120 - INFO - 設備行7231: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,130 - INFO - 設備行7238: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,142 - INFO - 設備行7246: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,144 - INFO - 設備行7247: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,149 - INFO - 設備行7250: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,152 - INFO - 設備行7252: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,168 - INFO - 設備行7263: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,189 - INFO - 設備行7278: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:37:49,191 - INFO - 設備行7279: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,193 - INFO - 設備行7280: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,196 - INFO - 設備行7282: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,198 - INFO - 設備行7283: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,200 - INFO - 設備行7284: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,203 - INFO - 設備行7286: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,206 - INFO - 設備行7288: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,207 - INFO - 設備行7289: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,212 - INFO - 設備行7292: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,228 - INFO - 設備行7303: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:49,241 - INFO - 設備行7312: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,247 - INFO - 設備行7316: 失敗項目I_PS1_min, 使用Bin值32
2025-07-29 09:37:49,273 - INFO - 設備行7334: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,275 - INFO - 設備行7335: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,277 - INFO - 設備行7336: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,281 - INFO - 設備行7339: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,296 - INFO - 設備行7349: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,304 - INFO - 設備行7355: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,306 - INFO - 設備行7356: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,308 - INFO - 設備行7357: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,310 - INFO - 設備行7358: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,327 - INFO - 設備行7370: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,329 - INFO - 設備行7371: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,334 - INFO - 設備行7374: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,338 - INFO - 設備行7377: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,340 - INFO - 設備行7378: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,345 - INFO - 設備行7381: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,346 - INFO - 設備行7382: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,357 - INFO - 設備行7389: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,359 - INFO - 設備行7390: 失敗項目SDA_iil, 使用Bin值19
2025-07-29 09:37:49,377 - INFO - 設備行7403: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:49,380 - INFO - 設備行7405: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,387 - INFO - 設備行7409: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,389 - INFO - 設備行7410: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,392 - INFO - 設備行7412: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,394 - INFO - 設備行7413: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:49,414 - INFO - 設備行7427: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,420 - INFO - 設備行7431: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,446 - INFO - 設備行7449: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,496 - INFO - 設備行7485: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,501 - INFO - 設備行7488: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,521 - INFO - 設備行7502: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,529 - INFO - 設備行7507: 失敗項目I_PS1_min, 使用Bin值32
2025-07-29 09:37:49,531 - INFO - 設備行7508: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,537 - INFO - 設備行7512: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,546 - INFO - 設備行7518: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,553 - INFO - 設備行7522: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,563 - INFO - 設備行7529: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,573 - INFO - 設備行7536: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,595 - INFO - 設備行7551: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,624 - INFO - 設備行7571: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,633 - INFO - 設備行7577: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,637 - INFO - 設備行7579: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,639 - INFO - 設備行7580: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,645 - INFO - 設備行7584: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:37:49,670 - INFO - 設備行7602: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,672 - INFO - 設備行7603: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,687 - INFO - 設備行7613: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,689 - INFO - 設備行7614: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,695 - INFO - 設備行7618: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,725 - INFO - 設備行7639: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,727 - INFO - 設備行7640: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,743 - INFO - 設備行7651: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,745 - INFO - 設備行7652: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:37:49,747 - INFO - 設備行7653: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,773 - INFO - 設備行7671: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,782 - INFO - 設備行7677: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,784 - INFO - 設備行7678: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,803 - INFO - 設備行7691: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,810 - INFO - 設備行7696: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,816 - INFO - 設備行7700: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,820 - INFO - 設備行7703: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,822 - INFO - 設備行7704: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,825 - INFO - 設備行7706: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,829 - INFO - 設備行7708: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,830 - INFO - 設備行7709: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,832 - INFO - 設備行7710: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:49,834 - INFO - 設備行7711: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,836 - INFO - 設備行7712: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,839 - INFO - 設備行7714: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,846 - INFO - 設備行7719: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,858 - INFO - 設備行7727: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,861 - INFO - 設備行7729: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,872 - INFO - 設備行7736: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,876 - INFO - 設備行7739: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,891 - INFO - 設備行7749: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,900 - INFO - 設備行7755: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,907 - INFO - 設備行7760: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,922 - INFO - 設備行7770: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:49,940 - INFO - 設備行7782: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,946 - INFO - 設備行7786: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,955 - INFO - 設備行7792: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:49,959 - INFO - 設備行7795: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,961 - INFO - 設備行7796: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:49,973 - INFO - 設備行7804: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:50,001 - INFO - 設備行7823: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,007 - INFO - 設備行7827: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,016 - INFO - 設備行7833: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,031 - INFO - 設備行7843: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,032 - INFO - 設備行7844: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,046 - INFO - 設備行7853: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,048 - INFO - 設備行7854: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:50,057 - INFO - 設備行7860: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:50,061 - INFO - 設備行7863: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:50,063 - INFO - 設備行7864: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,066 - INFO - 設備行7866: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,072 - INFO - 設備行7870: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,074 - INFO - 設備行7871: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:50,085 - INFO - 設備行7878: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:50,088 - INFO - 設備行7880: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,091 - INFO - 設備行7882: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:37:50,109 - INFO - 設備行7894: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,122 - INFO - 設備行7903: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,127 - INFO - 設備行7906: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,135 - INFO - 設備行7911: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,138 - INFO - 設備行7913: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,140 - INFO - 設備行7914: 失敗項目IQ_typ, 使用Bin值30
2025-07-29 09:37:50,143 - INFO - 設備行7916: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,153 - INFO - 設備行7923: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:50,188 - INFO - 設備行7947: 失敗項目PS2_UVP_R, 使用Bin值91
2025-07-29 09:37:50,200 - INFO - 設備行7955: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,202 - INFO - 設備行7956: 失敗項目PS2OUT_iil, 使用Bin值18
2025-07-29 09:37:50,205 - INFO - 設備行7958: 失敗項目EN_F, 使用Bin值45
2025-07-29 09:37:50,213 - INFO - 設備行7963: 失敗項目P1_PS1IN, 使用Bin值7
2025-07-29 09:37:50,222 - INFO - 設備行7969: 失敗項目EN_hys, 使用Bin值46
2025-07-29 09:37:50,263 - INFO - 重新計算並更新了7959個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 09:37:50,264 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 11.90s
2025-07-29 09:37:50,264 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 2706.98s
2025-07-29 09:37:50,264 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 09:37:50,265 - INFO - 應用Device2BinControl處理...
2025-07-29 09:37:50,265 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 09:37:50,266 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 09:37:50,266 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 09:37:50,266 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-29 09:37:50,266 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 09:37:50,267 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-29 09:37:50,267 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 09:37:51,072 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 09:37:51,072 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 805.0ms
2025-07-29 09:37:51,073 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 09:37:51,073 - INFO - ✅ 數據摘要: 項目108個, 設備7959個, Site2個
2025-07-29 09:37:51,074 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 09:37:51,074 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 09:37:51,074 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:37:51,075 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:37:51,075 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:37:51,075 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:37:51,076 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 09:37:51,076 - INFO - 使用統一數據: 設備7959個, 項目108個
2025-07-29 09:37:51,083 - INFO - 收集原始Bin值: 7959個設備
2025-07-29 09:38:09,716 - INFO - 應用染色邏輯: 1330個設備的失敗項目
2025-07-29 09:38:09,741 - INFO - Bin統計: Pass=6629, Fail=1330, Total=7959
2025-07-29 09:38:09,742 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 09:38:09,742 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 09:38:09,744 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 18.67s
2025-07-29 09:38:09,745 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 09:38:09,745 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:38:09,745 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:38:09,746 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:38:09,746 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:38:09,747 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:38:09,747 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:38:14,708 - INFO - ⚡ 已處理78000個cell的字體顏色
2025-07-29 09:38:21,526 - INFO - ⚡ 已處理185000個cell的字體顏色
2025-07-29 09:38:46,968 - INFO - ⚡ 已處理585000個cell的字體顏色
2025-07-29 09:38:52,634 - INFO - ⚡ 已處理675000個cell的字體顏色
2025-07-29 09:39:00,370 - INFO - ⚡ 已處理795000個cell的字體顏色
2025-07-29 09:39:03,382 - INFO - ⚡ 超級批量字體設置完成，總共處理843296個cell
2025-07-29 09:39:03,382 - INFO - 設置數據區域字體顏色: 行13-7971, 列2-110
2025-07-29 09:39:03,383 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 53.64s
2025-07-29 09:39:03,383 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 73.12s
2025-07-29 09:39:03,383 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 09:39:03,384 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 09:39:03,384 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 09:39:03,385 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:39:03,385 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:39:03,385 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:39:03,385 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:39:03,386 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 09:39:03,386 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:39:03,386 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:39:03,386 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:39:03,387 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:39:03,387 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:39:03,387 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:39:03,387 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 09:39:03,387 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 09:39:03,388 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 09:39:03,388 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-29 09:39:03,388 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 09:39:03,389 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-29 09:39:03,389 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 09:39:04,164 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 09:39:04,164 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 775.1ms
2025-07-29 09:39:04,165 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 09:39:04,165 - INFO - ✅ 數據摘要: 項目108個, 設備7959個, Site2個
2025-07-29 09:39:04,166 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 09:39:04,166 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 09:39:04,166 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:39:04,167 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:39:04,167 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:39:04,167 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:39:04,167 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 09:39:04,167 - INFO - 使用統一數據: 設備7959個, 項目108個
2025-07-29 09:39:04,174 - INFO - 收集原始Bin值: 7959個設備
2025-07-29 09:39:22,776 - INFO - 應用染色邏輯: 1330個設備的失敗項目
2025-07-29 09:39:22,800 - INFO - Bin統計: Pass=6629, Fail=1330, Total=7959
2025-07-29 09:39:22,801 - INFO - VBA 457-469行：填充基本統計 Total=7959, Pass=6629, Fail=1330, Yield=83.29%
2025-07-29 09:39:22,804 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_q1wjj6el\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300.spd
2025-07-29 09:39:23,193 - INFO - VBA 362-420行（修正版）：填充了17個測試項目的Bin數據，無重複
2025-07-29 09:39:23,201 - INFO - 創建Site統計完成: 最大Site號=2，實際有設備的Site數=2
2025-07-29 09:39:23,202 - INFO -   Site 1: 3758個設備
2025-07-29 09:39:23,202 - INFO -   Site 2: 4201個設備
2025-07-29 09:39:23,203 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-29 09:39:23,204 - INFO - 填充Site 1統計完成: 3758個設備
2025-07-29 09:39:23,205 - INFO - 填充Site 2統計完成: 4201個設備
2025-07-29 09:39:23,205 - INFO - Site統計填充完成: 2個Site
2025-07-29 09:39:23,206 - INFO - 設置 AutoFilter 範圍: A6:I23
2025-07-29 09:39:23,208 - INFO - 按 B 欄由大到小排序了 17 行資料
2025-07-29 09:39:23,209 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-29 09:39:23,209 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 09:39:23,210 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 09:39:23,212 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 19.05s
2025-07-29 09:39:23,213 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 09:39:23,213 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:39:23,214 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:39:23,214 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:39:23,214 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:39:23,215 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:39:23,215 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:39:31,030 - INFO - ⚡ 已處理78000個cell的字體顏色
2025-07-29 09:39:41,672 - INFO - ⚡ 已處理185000個cell的字體顏色
2025-07-29 09:40:16,843 - INFO - ⚡ 已處理585000個cell的字體顏色
2025-07-29 09:40:21,242 - INFO - ⚡ 已處理675000個cell的字體顏色
2025-07-29 09:40:27,178 - INFO - ⚡ 已處理795000個cell的字體顏色
2025-07-29 09:40:29,570 - INFO - ⚡ 超級批量字體設置完成，總共處理843296個cell
2025-07-29 09:40:29,571 - INFO - 設置數據區域字體顏色: 行13-7971, 列2-110
2025-07-29 09:40:29,571 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 66.36s
2025-07-29 09:40:29,572 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 86.18s
2025-07-29 09:40:29,572 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 09:40:29,572 - INFO - === 統一處理管道完成 ===
2025-07-29 09:41:55,394 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 09:42:05,435 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300_spd_converted.xlsx，耗時10040.6ms
2025-07-29 09:42:05,436 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G5440WC(CB)_GHBR03.1_01_CGM24090038_CP1_20240904021300_spd_converted.xlsx
2025-07-29 09:42:05,443 - INFO - 開始完整轉換...
2025-07-29 09:42:05,443 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-29 09:42:05,479 - INFO - 成功讀取CSV文件，大小: (911, 110)
2025-07-29 09:42:05,480 - INFO - 檢測到TMT測試儀格式（通過A1=Spreadsheet）
2025-07-29 09:42:05,481 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 09:42:05,481 - INFO - 步驟4: 檢測到TMT格式，直接進入步驟6
2025-07-29 09:42:05,482 - INFO - 創建TMT格式工作簿...
2025-07-29 09:42:06,179 - INFO - ⚡ 優化寫入完成: 911行 × 110列，耗時696.0ms
2025-07-29 09:42:06,180 - INFO - 步驟5：在9A位置標記原始格式為TMT...
2025-07-29 09:42:06,180 - INFO - TMT格式不需要sum分頁
2025-07-29 09:42:06,181 - INFO - TMT工作簿創建完成：僅Data11工作表 911行，無sum分頁
2025-07-29 09:42:06,181 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 09:42:06,181 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 09:42:06,182 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 09:42:06,182 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 09:42:06,182 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 09:42:06,182 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 09:42:06,183 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 09:42:06,183 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 09:42:06,183 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 09:42:06,183 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 09:42:06,184 - INFO - 🔍 開始統一數據收集...
2025-07-29 09:42:06,184 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 09:42:06,425 - INFO - 收集項目數據: 108個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 09:42:06,425 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 09:42:36,376 - INFO - 收集設備數據: 899個設備（已排除無測試數據的行）
2025-07-29 09:42:36,377 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 09:42:36,405 - INFO - 找到Site列: 第8行第4列，標題: Site
2025-07-29 09:42:36,408 - INFO - 收集Site數據: 最大Site號=2，實際Site數=2，899筆Site資料
2025-07-29 09:42:36,409 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 09:42:36,410 - INFO - 收集限制值: Max 108個, Min 108個
2025-07-29 09:42:36,410 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 09:42:36,411 - INFO - 設置第6行項目編號: 從第3列開始，共108個項目
2025-07-29 09:42:36,411 - INFO - ⏱️ 統一數據收集 執行時間: 30.23s
2025-07-29 09:42:36,411 - INFO - ✅ 統一數據收集完成: 項目108個, 設備899個, Site2個
2025-07-29 09:42:36,411 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 30.23s
2025-07-29 09:42:36,412 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 09:42:36,412 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 09:42:36,412 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 09:42:36,413 - INFO -   - 設置紅色字體標記
2025-07-29 09:42:36,413 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 1.0ms
2025-07-29 09:42:36,413 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 09:42:36,414 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 09:42:36,414 - INFO -   - 填充預設限制值
2025-07-29 09:42:36,414 - INFO -   - 設置紅色字體標記
2025-07-29 09:42:36,416 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 3.0ms
2025-07-29 09:42:36,416 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 09:42:36,416 - INFO -   - 驗證數據完整性
2025-07-29 09:42:36,416 - INFO -   - 準備統一數據摘要
2025-07-29 09:42:36,417 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 09:42:36,417 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 09:42:36,417 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 09:42:36,425 - INFO - 設備行23: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,430 - INFO - 設備行28: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,464 - INFO - 設備行72: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,483 - INFO - 設備行97: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,505 - INFO - 設備行125: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,511 - INFO - 設備行132: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:42:36,540 - INFO - 設備行170: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:42:36,577 - INFO - 設備行218: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,614 - INFO - 設備行265: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,617 - INFO - 設備行268: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,635 - INFO - 設備行292: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,638 - INFO - 設備行295: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,690 - INFO - 設備行361: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,692 - INFO - 設備行363: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,729 - INFO - 設備行410: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,746 - INFO - 設備行431: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,757 - INFO - 設備行444: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,761 - INFO - 設備行449: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,815 - INFO - 設備行517: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,835 - INFO - 設備行543: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,841 - INFO - 設備行550: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,848 - INFO - 設備行558: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,860 - INFO - 設備行574: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,873 - INFO - 設備行590: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:36,907 - INFO - 設備行633: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,911 - INFO - 設備行638: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:36,975 - INFO - 設備行719: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,014 - INFO - 設備行767: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,050 - INFO - 設備行813: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,052 - INFO - 設備行815: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,067 - INFO - 設備行835: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,072 - INFO - 設備行840: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:42:37,085 - INFO - 設備行856: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,092 - INFO - 設備行865: 失敗項目PS1_UVP_R, 使用Bin值88
2025-07-29 09:42:37,095 - INFO - 設備行868: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,097 - INFO - 設備行870: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,103 - INFO - 設備行876: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,104 - INFO - 設備行877: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,105 - INFO - 設備行878: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,107 - INFO - 設備行879: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,108 - INFO - 設備行880: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,109 - INFO - 設備行881: 失敗項目IQ_min, 使用Bin值29
2025-07-29 09:42:37,110 - INFO - 設備行882: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:42:37,111 - INFO - 設備行883: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,113 - INFO - 設備行884: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,114 - INFO - 設備行885: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,115 - INFO - 設備行886: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,116 - INFO - 設備行887: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,117 - INFO - 設備行888: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,119 - INFO - 設備行889: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,120 - INFO - 設備行890: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,121 - INFO - 設備行891: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,122 - INFO - 設備行892: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,123 - INFO - 設備行893: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,124 - INFO - 設備行894: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,126 - INFO - 設備行895: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,127 - INFO - 設備行896: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,128 - INFO - 設備行897: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,130 - INFO - 設備行898: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,131 - INFO - 設備行899: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,132 - INFO - 設備行900: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,133 - INFO - 設備行901: 失敗項目PS2OUT_100mA, 使用Bin值49
2025-07-29 09:42:37,134 - INFO - 設備行902: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,136 - INFO - 設備行903: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,137 - INFO - 設備行904: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,138 - INFO - 設備行905: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,140 - INFO - 設備行906: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,141 - INFO - 設備行907: 失敗項目PS2_UVP_hys, 使用Bin值93
2025-07-29 09:42:37,142 - INFO - 設備行908: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,144 - INFO - 設備行909: 失敗項目PS1_UVP_R, 使用Bin值88
2025-07-29 09:42:37,145 - INFO - 設備行910: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,146 - INFO - 設備行911: 失敗項目SAS_L_PS2, 使用Bin值42
2025-07-29 09:42:37,149 - INFO - 重新計算並更新了899個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 09:42:37,149 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 732.0ms
2025-07-29 09:42:37,149 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 30.97s
2025-07-29 09:42:37,149 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 09:42:37,150 - INFO - 應用Device2BinControl處理...
2025-07-29 09:42:37,150 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 09:42:37,150 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 09:42:37,150 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 09:42:37,151 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-29 09:42:37,151 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 09:42:37,151 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-29 09:42:37,151 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 09:42:37,212 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 09:42:37,212 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 61.0ms
2025-07-29 09:42:37,213 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 09:42:37,213 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-29 09:42:37,214 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 09:42:37,214 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 09:42:37,214 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:42:37,214 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:42:37,215 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:42:37,215 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:42:37,215 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 09:42:37,215 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-29 09:42:37,216 - INFO - 收集原始Bin值: 899個設備
2025-07-29 09:42:38,214 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-29 09:42:38,217 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-29 09:42:38,217 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 09:42:38,217 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 09:42:38,218 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.00s
2025-07-29 09:42:38,218 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 09:42:38,218 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:42:38,219 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:42:38,219 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:42:38,219 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:42:38,219 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:42:38,220 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:42:39,416 - INFO - ⚡ 已處理34000個cell的字體顏色
2025-07-29 09:42:40,274 - INFO - ⚡ 已處理59000個cell的字體顏色
2025-07-29 09:42:41,541 - INFO - ⚡ 超級批量字體設置完成，總共處理96621個cell
2025-07-29 09:42:41,541 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-29 09:42:41,542 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 3.32s
2025-07-29 09:42:41,542 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 4.39s
2025-07-29 09:42:41,542 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 09:42:41,543 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 09:42:41,543 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 09:42:41,543 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:42:41,544 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:42:41,544 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:42:41,544 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:42:41,545 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 09:42:41,545 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:42:41,545 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:42:41,546 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:42:41,546 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:42:41,546 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:42:41,546 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:42:41,547 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 09:42:41,547 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 09:42:41,548 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 09:42:41,548 - INFO - 步驟7：讀取步驟5設定的原始格式標記: TMT
2025-07-29 09:42:41,549 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 09:42:41,549 - INFO - ✅ 步驟2：原始格式類型: TMT
2025-07-29 09:42:41,550 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 09:42:41,607 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 09:42:41,608 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 58.0ms
2025-07-29 09:42:41,609 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 09:42:41,609 - INFO - ✅ 數據摘要: 項目108個, 設備899個, Site2個
2025-07-29 09:42:41,609 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: 1.0ms
2025-07-29 09:42:41,610 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 09:42:41,610 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:42:41,610 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:42:41,611 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:42:41,611 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:42:41,611 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 09:42:41,611 - INFO - 使用統一數據: 設備899個, 項目108個
2025-07-29 09:42:41,612 - INFO - 收集原始Bin值: 899個設備
2025-07-29 09:42:42,599 - INFO - 應用染色邏輯: 72個設備的失敗項目
2025-07-29 09:42:42,601 - INFO - Bin統計: Pass=827, Fail=72, Total=899
2025-07-29 09:42:42,602 - INFO - VBA 457-469行：填充基本統計 Total=899, Pass=827, Fail=72, Yield=91.99%
2025-07-29 09:42:42,604 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/G5440WC(CC)_GHBR04.1_02.spd
2025-07-29 09:42:42,635 - INFO - VBA 362-420行（修正版）：填充了6個測試項目的Bin數據，無重複
2025-07-29 09:42:42,636 - INFO - 創建Site統計完成: 最大Site號=2，實際有設備的Site數=2
2025-07-29 09:42:42,636 - INFO -   Site 1: 439個設備
2025-07-29 09:42:42,637 - INFO -   Site 2: 460個設備
2025-07-29 09:42:42,637 - INFO - 開始填充Site統計: 2個Site, 實際項目數量: 0
2025-07-29 09:42:42,638 - INFO - 填充Site 1統計完成: 439個設備
2025-07-29 09:42:42,638 - INFO - 填充Site 2統計完成: 460個設備
2025-07-29 09:42:42,639 - INFO - Site統計填充完成: 2個Site
2025-07-29 09:42:42,639 - INFO - 設置 AutoFilter 範圍: A6:I12
2025-07-29 09:42:42,640 - INFO - 按 B 欄由大到小排序了 6 行資料
2025-07-29 09:42:42,640 - INFO - 自動調整D列寬度: 16.4 (基於最大內容長度: 12)
2025-07-29 09:42:42,641 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 09:42:42,641 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 09:42:42,642 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 1.03s
2025-07-29 09:42:42,642 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 09:42:42,642 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:42:42,643 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:42:42,643 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:42:42,643 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:42:42,644 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:42:42,644 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:42:44,507 - INFO - ⚡ 已處理34000個cell的字體顏色
2025-07-29 09:42:45,859 - INFO - ⚡ 已處理59000個cell的字體顏色
2025-07-29 09:42:47,946 - INFO - ⚡ 超級批量字體設置完成，總共處理96621個cell
2025-07-29 09:42:47,947 - INFO - 設置數據區域字體顏色: 行13-911, 列2-110
2025-07-29 09:42:47,947 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 5.31s
2025-07-29 09:42:47,948 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 6.40s
2025-07-29 09:42:47,948 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 09:42:47,948 - INFO - === 統一處理管道完成 ===
2025-07-29 09:42:57,076 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 09:42:58,183 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx，耗時1107.0ms
2025-07-29 09:42:58,184 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G5440WC(CC)_GHBR04.1_02_spd_converted.xlsx
2025-07-29 09:42:58,186 - INFO - 開始完整轉換...
2025-07-29 09:42:58,186 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-29 09:42:58,212 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-29 09:42:58,219 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-29 09:42:58,220 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 09:42:58,220 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-29 09:42:58,221 - INFO - 執行CTA到TMT格式轉換...
2025-07-29 09:42:59,745 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時1515.1ms
2025-07-29 09:42:59,760 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時13.1ms
2025-07-29 09:42:59,764 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時3.0ms
2025-07-29 09:42:59,764 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時1536.0ms
2025-07-29 09:42:59,765 - INFO - 應用CTA8280轉換處理...
2025-07-29 09:42:59,765 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-29 09:42:59,765 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-29 09:42:59,766 - INFO - 找到標題行在第2行
2025-07-29 09:42:59,766 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-29 09:42:59,767 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-29 09:42:59,767 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: 1.0ms
2025-07-29 09:42:59,767 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-29 09:42:59,769 - INFO - myFindedRowN=2, myColumnN=14
2025-07-29 09:42:59,803 - INFO - ⚡ 超級批量插入6行完成，耗時6.0ms
2025-07-29 09:42:59,803 - INFO - 插入了6行在最前面
2025-07-29 09:42:59,804 - INFO - ⚡ 標題行插入優化完成，耗時6.0ms
2025-07-29 09:42:59,804 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 37.0ms
2025-07-29 09:42:59,805 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-29 09:42:59,805 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-29 09:42:59,805 - INFO - 清空位置 A8: "Index_No"
2025-07-29 09:42:59,806 - INFO - 清空位置 B8: "Dut_No"
2025-07-29 09:42:59,806 - INFO - 清空位置 A10: "ASD"
2025-07-29 09:42:59,806 - INFO - 清空位置 B10: "QQ"
2025-07-29 09:42:59,806 - INFO - 清空位置 A11: "A"
2025-07-29 09:42:59,807 - INFO - 清空位置 B11: "B"
2025-07-29 09:42:59,807 - INFO - ✅ 清空了6個多餘資料位置
2025-07-29 09:42:59,808 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-29 09:42:59,809 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 4.0ms
2025-07-29 09:42:59,810 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-29 09:42:59,811 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 1.0ms
2025-07-29 09:42:59,812 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-29 09:42:59,813 - INFO - 找到SW_Bin列在第6列
2025-07-29 09:42:59,813 - INFO - 處理了17個設備的數據
2025-07-29 09:42:59,813 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 1.0ms
2025-07-29 09:42:59,814 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 49.0ms
2025-07-29 09:42:59,814 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-29 09:42:59,814 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-29 09:42:59,815 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 09:42:59,815 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 09:42:59,815 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 09:42:59,816 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 09:42:59,816 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 09:42:59,816 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 09:42:59,816 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 09:42:59,817 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 09:42:59,817 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 09:42:59,817 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 09:42:59,817 - INFO - 🔍 開始統一數據收集...
2025-07-29 09:42:59,818 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 09:42:59,821 - INFO - 收集項目數據: 58個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 09:42:59,821 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 09:42:59,829 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-29 09:42:59,829 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 09:42:59,830 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-29 09:42:59,831 - INFO - 收集Site數據: 最大Site號=4，實際Site數=4，16筆Site資料
2025-07-29 09:42:59,831 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 09:42:59,831 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-29 09:42:59,832 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 09:42:59,832 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-29 09:42:59,833 - INFO - ⏱️ 統一數據收集 執行時間: 14.0ms
2025-07-29 09:42:59,833 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-29 09:42:59,833 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 16.0ms
2025-07-29 09:42:59,833 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 09:42:59,834 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 09:42:59,834 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 09:42:59,834 - INFO -   - 設置紅色字體標記
2025-07-29 09:42:59,837 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 4.0ms
2025-07-29 09:42:59,837 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 09:42:59,838 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 09:42:59,838 - INFO -   - 填充預設限制值
2025-07-29 09:42:59,838 - INFO -   - 設置紅色字體標記
2025-07-29 09:42:59,842 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 5.0ms
2025-07-29 09:42:59,843 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 09:42:59,843 - INFO -   - 驗證數據完整性
2025-07-29 09:42:59,844 - INFO -   - 準備統一數據摘要
2025-07-29 09:42:59,844 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 09:42:59,844 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 09:42:59,844 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 09:42:59,846 - INFO - 設備行14: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,846 - INFO - 設備行15: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,847 - INFO - 設備行16: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,848 - INFO - 設備行18: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,849 - INFO - 設備行19: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,850 - INFO - 設備行20: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,851 - INFO - 設備行22: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,852 - INFO - 設備行23: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,853 - INFO - 設備行24: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,854 - INFO - 設備行26: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,855 - INFO - 設備行27: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,855 - INFO - 設備行28: 失敗項目Site_Check, 使用Bin值39
2025-07-29 09:42:59,856 - INFO - 重新計算並更新了17個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 09:42:59,857 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 13.0ms
2025-07-29 09:42:59,857 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 40.0ms
2025-07-29 09:42:59,857 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 09:42:59,857 - INFO - 應用Device2BinControl處理...
2025-07-29 09:42:59,858 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 09:42:59,858 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 09:42:59,858 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 09:42:59,858 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 09:42:59,859 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 09:42:59,859 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 09:42:59,859 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 09:42:59,861 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 09:42:59,861 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 2.0ms
2025-07-29 09:42:59,862 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 09:42:59,862 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-29 09:42:59,862 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 09:42:59,862 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 09:42:59,863 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:42:59,863 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:42:59,863 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:42:59,864 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:42:59,864 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 09:42:59,864 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-29 09:42:59,865 - INFO - 收集原始Bin值: 17個設備
2025-07-29 09:42:59,874 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-29 09:42:59,874 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-29 09:42:59,875 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 09:42:59,875 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 09:42:59,875 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 13.0ms
2025-07-29 09:42:59,875 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 09:42:59,876 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:42:59,876 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:42:59,876 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:42:59,877 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:42:59,877 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:42:59,877 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:42:59,927 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-29 09:42:59,928 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-29 09:42:59,928 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 53.0ms
2025-07-29 09:42:59,929 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 71.0ms
2025-07-29 09:42:59,929 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 09:42:59,929 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 09:42:59,930 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 09:42:59,930 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:42:59,930 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:42:59,930 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:42:59,931 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:42:59,931 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 09:42:59,931 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:42:59,931 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:42:59,931 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:42:59,932 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:42:59,932 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:42:59,932 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:42:59,932 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 09:42:59,932 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 09:42:59,933 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 09:42:59,933 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 09:42:59,933 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 09:42:59,934 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 09:42:59,934 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 09:42:59,935 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 09:42:59,935 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 1.0ms
2025-07-29 09:42:59,936 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 09:42:59,936 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-29 09:42:59,936 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 09:42:59,936 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 09:42:59,936 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 09:42:59,937 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 09:42:59,937 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 09:42:59,937 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 09:42:59,937 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 09:42:59,937 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-29 09:42:59,938 - INFO - 收集原始Bin值: 17個設備
2025-07-29 09:42:59,947 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-29 09:42:59,948 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-29 09:42:59,949 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-29 09:42:59,950 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-29 09:42:59,951 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-29 09:42:59,951 - INFO - 創建Site統計完成: 最大Site號=4，實際有設備的Site數=4
2025-07-29 09:42:59,952 - INFO -   Site 1: 4個設備
2025-07-29 09:42:59,952 - INFO -   Site 2: 4個設備
2025-07-29 09:42:59,952 - INFO -   Site 3: 4個設備
2025-07-29 09:42:59,952 - INFO -   Site 4: 4個設備
2025-07-29 09:42:59,953 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-29 09:42:59,953 - INFO - 填充Site 1統計完成: 4個設備
2025-07-29 09:42:59,953 - INFO - 填充Site 2統計完成: 4個設備
2025-07-29 09:42:59,954 - INFO - 填充Site 3統計完成: 4個設備
2025-07-29 09:42:59,954 - INFO - 填充Site 4統計完成: 4個設備
2025-07-29 09:42:59,954 - INFO - Site統計填充完成: 4個Site
2025-07-29 09:42:59,954 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-29 09:42:59,955 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-29 09:42:59,955 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-29 09:42:59,955 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 09:42:59,956 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 09:42:59,956 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 20.0ms
2025-07-29 09:42:59,956 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 09:42:59,956 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 09:42:59,957 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 09:42:59,957 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 09:42:59,957 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 09:42:59,957 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 09:42:59,957 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 09:43:00,032 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-29 09:43:00,033 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-29 09:43:00,033 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 77.0ms
2025-07-29 09:43:00,034 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 102.0ms
2025-07-29 09:43:00,034 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 09:43:00,035 - INFO - === 統一處理管道完成 ===
2025-07-29 09:43:03,884 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 09:43:04,357 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx，耗時473.0ms
2025-07-29 09:43:04,358 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx
2025-07-29 09:44:30,829 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_gvxwmn2a
2025-07-29 09:44:30,834 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_q1wjj6el
2025-07-29 11:38:48,118 - INFO - 開始完整轉換...
2025-07-29 11:38:48,118 - INFO - 讀取CSV文件: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-29 11:38:48,167 - INFO - 成功讀取CSV文件，大小: (3511, 60)
2025-07-29 11:38:48,169 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-29 11:38:48,169 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 11:38:48,169 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-29 11:38:48,170 - INFO - 執行CTA到TMT格式轉換...
2025-07-29 11:38:48,389 - INFO - ⚡ 優化寫入完成: 3484行 × 60列，耗時215.6ms
2025-07-29 11:38:48,392 - INFO - ⚡ 優化寫入完成: 22行 × 60列，耗時2.2ms
2025-07-29 11:38:48,393 - INFO - ⚡ 優化寫入完成: 4行 × 60列，耗時0.5ms
2025-07-29 11:38:48,393 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時221.2ms
2025-07-29 11:38:48,393 - INFO - 應用CTA8280轉換處理...
2025-07-29 11:38:48,394 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-29 11:38:48,394 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-29 11:38:48,394 - INFO - 找到標題行在第2行
2025-07-29 11:38:48,394 - INFO - ⏱️ 步驟1-找到標題行 執行時間: <1ms
2025-07-29 11:38:48,394 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-29 11:38:48,395 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-29 11:38:48,395 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-29 11:38:48,395 - INFO - myFindedRowN=2, myColumnN=14
2025-07-29 11:38:48,400 - INFO - ⚡ 超級批量插入6行完成，耗時0.9ms
2025-07-29 11:38:48,401 - INFO - 插入了6行在最前面
2025-07-29 11:38:48,401 - INFO - ⚡ 標題行插入優化完成，耗時1.1ms
2025-07-29 11:38:48,401 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 6.1ms
2025-07-29 11:38:48,401 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-29 11:38:48,401 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-29 11:38:48,401 - INFO - 清空位置 A8: "Index_No"
2025-07-29 11:38:48,402 - INFO - 清空位置 B8: "Dut_No"
2025-07-29 11:38:48,402 - INFO - 清空位置 A10: "ASD"
2025-07-29 11:38:48,402 - INFO - 清空位置 B10: "QQ"
2025-07-29 11:38:48,402 - INFO - 清空位置 A11: "A"
2025-07-29 11:38:48,402 - INFO - 清空位置 B11: "B"
2025-07-29 11:38:48,402 - INFO - ✅ 清空了6個多餘資料位置
2025-07-29 11:38:48,402 - INFO - 最後一列資料密度正常(31.0%)，保留資料
2025-07-29 11:38:48,403 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 1.6ms
2025-07-29 11:38:48,403 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-29 11:38:48,404 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: <1ms
2025-07-29 11:38:48,404 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-29 11:38:48,404 - INFO - 找到SW_Bin列在第6列
2025-07-29 11:38:48,404 - INFO - 處理了17個設備的數據
2025-07-29 11:38:48,404 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: <1ms
2025-07-29 11:38:48,404 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 10.6ms
2025-07-29 11:38:48,404 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-29 11:38:48,404 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-29 11:38:48,404 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 11:38:48,404 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 11:38:48,405 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 11:38:48,405 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 11:38:48,405 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 11:38:48,405 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 11:38:48,405 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 11:38:48,405 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 11:38:48,405 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 11:38:48,406 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 11:38:48,406 - INFO - 🔍 開始統一數據收集...
2025-07-29 11:38:48,406 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 11:38:48,408 - INFO - 收集項目數據: 58個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 11:38:48,409 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 11:38:48,411 - INFO - 收集設備數據: 17個設備（已排除無測試數據的行）
2025-07-29 11:38:48,411 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 11:38:48,412 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-29 11:38:48,412 - INFO - 收集Site數據: 最大Site號=4，實際Site數=4，16筆Site資料
2025-07-29 11:38:48,413 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 11:38:48,413 - INFO - 收集限制值: Max 45個, Min 45個
2025-07-29 11:38:48,413 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 11:38:48,414 - INFO - 設置第6行項目編號: 從第3列開始，共58個項目
2025-07-29 11:38:48,414 - INFO - ⏱️ 統一數據收集 執行時間: 7.7ms
2025-07-29 11:38:48,414 - INFO - ✅ 統一數據收集完成: 項目58個, 設備17個, Site4個
2025-07-29 11:38:48,415 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 8.9ms
2025-07-29 11:38:48,415 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 11:38:48,415 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 11:38:48,415 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 11:38:48,416 - INFO -   - 設置紅色字體標記
2025-07-29 11:38:48,417 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 1.8ms
2025-07-29 11:38:48,417 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 11:38:48,417 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 11:38:48,417 - INFO -   - 填充預設限制值
2025-07-29 11:38:48,418 - INFO -   - 設置紅色字體標記
2025-07-29 11:38:48,419 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 2.0ms
2025-07-29 11:38:48,419 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 11:38:48,419 - INFO -   - 驗證數據完整性
2025-07-29 11:38:48,420 - INFO -   - 準備統一數據摘要
2025-07-29 11:38:48,420 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 11:38:48,420 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 11:38:48,421 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 11:38:48,421 - INFO - 設備行14: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,422 - INFO - 設備行15: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,422 - INFO - 設備行16: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,422 - INFO - 設備行18: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,423 - INFO - 設備行19: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,423 - INFO - 設備行20: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,424 - INFO - 設備行22: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,424 - INFO - 設備行23: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,424 - INFO - 設備行24: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,425 - INFO - 設備行26: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,425 - INFO - 設備行27: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,426 - INFO - 設備行28: 失敗項目Site_Check, 使用Bin值39
2025-07-29 11:38:48,426 - INFO - 重新計算並更新了17個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 11:38:48,427 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 6.2ms
2025-07-29 11:38:48,427 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 21.5ms
2025-07-29 11:38:48,427 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 11:38:48,427 - INFO - 應用Device2BinControl處理...
2025-07-29 11:38:48,428 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 11:38:48,428 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 11:38:48,428 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 11:38:48,428 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 11:38:48,429 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 11:38:48,429 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 11:38:48,429 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 11:38:48,430 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 11:38:48,430 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: <1ms
2025-07-29 11:38:48,430 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 11:38:48,431 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-29 11:38:48,431 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 11:38:48,431 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 11:38:48,432 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:38:48,432 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:38:48,432 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:38:48,432 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:38:48,433 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 11:38:48,433 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-29 11:38:48,434 - INFO - 收集原始Bin值: 17個設備
2025-07-29 11:38:48,437 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-29 11:38:48,440 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-29 11:38:48,446 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 11:38:48,447 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 11:38:48,448 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 16.4ms
2025-07-29 11:38:48,448 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 11:38:48,449 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:38:48,449 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:38:48,449 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:38:48,450 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:38:48,450 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:38:48,450 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:38:48,468 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-29 11:38:48,469 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-29 11:38:48,471 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 22.8ms
2025-07-29 11:38:48,471 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 43.8ms
2025-07-29 11:38:48,472 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 11:38:48,472 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 11:38:48,472 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 11:38:48,472 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:38:48,473 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:38:48,473 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:38:48,473 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:38:48,473 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 11:38:48,474 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:38:48,474 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:38:48,474 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:38:48,474 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:38:48,475 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:38:48,475 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:38:48,475 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 11:38:48,476 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 11:38:48,476 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 11:38:48,476 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 11:38:48,477 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 11:38:48,477 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 11:38:48,477 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 11:38:48,477 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 11:38:48,477 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: <1ms
2025-07-29 11:38:48,478 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 11:38:48,478 - INFO - ✅ 數據摘要: 項目58個, 設備17個, Site4個
2025-07-29 11:38:48,478 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 11:38:48,478 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 11:38:48,479 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:38:48,479 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:38:48,479 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:38:48,479 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:38:48,479 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 11:38:48,479 - INFO - 使用統一數據: 設備17個, 項目58個
2025-07-29 11:38:48,480 - INFO - 收集原始Bin值: 17個設備
2025-07-29 11:38:48,482 - INFO - 應用染色邏輯: 12個設備的失敗項目
2025-07-29 11:38:48,483 - INFO - Bin統計: Pass=5, Fail=12, Total=17
2025-07-29 11:38:48,484 - INFO - VBA 457-469行：填充基本統計 Total=17, Pass=5, Fail=12, Yield=29.41%
2025-07-29 11:38:48,485 - INFO - 添加原始檔案超連結: C:/Users/<USER>/Desktop/test/GMT_G2304.csv
2025-07-29 11:38:48,486 - INFO - VBA 362-420行（修正版）：填充了2個測試項目的Bin數據，無重複
2025-07-29 11:38:48,486 - INFO - 創建Site統計完成: 最大Site號=4，實際有設備的Site數=4
2025-07-29 11:38:48,487 - INFO -   Site 1: 4個設備
2025-07-29 11:38:48,487 - INFO -   Site 2: 4個設備
2025-07-29 11:38:48,487 - INFO -   Site 3: 4個設備
2025-07-29 11:38:48,488 - INFO -   Site 4: 4個設備
2025-07-29 11:38:48,488 - INFO - 開始填充Site統計: 4個Site, 實際項目數量: 0
2025-07-29 11:38:48,489 - INFO - 填充Site 1統計完成: 4個設備
2025-07-29 11:38:48,489 - INFO - 填充Site 2統計完成: 4個設備
2025-07-29 11:38:48,489 - INFO - 填充Site 3統計完成: 4個設備
2025-07-29 11:38:48,489 - INFO - 填充Site 4統計完成: 4個設備
2025-07-29 11:38:48,490 - INFO - Site統計填充完成: 4個Site
2025-07-29 11:38:48,490 - INFO - 設置 AutoFilter 範圍: A6:M8
2025-07-29 11:38:48,490 - INFO - 按 B 欄由大到小排序了 2 行資料
2025-07-29 11:38:48,491 - INFO - 自動調整D列寬度: 14.0 (基於最大內容長度: 10)
2025-07-29 11:38:48,491 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 11:38:48,491 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 11:38:48,491 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 12.9ms
2025-07-29 11:38:48,492 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 11:38:48,492 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:38:48,492 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:38:48,492 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:38:48,493 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:38:48,493 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:38:48,493 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:38:48,511 - INFO - ⚡ 超級批量字體設置完成，總共處理991個cell
2025-07-29 11:38:48,512 - INFO - 設置數據區域字體顏色: 行13-29, 列2-60
2025-07-29 11:38:48,512 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 20.8ms
2025-07-29 11:38:48,513 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 37.3ms
2025-07-29 11:38:48,513 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 11:38:48,513 - INFO - === 統一處理管道完成 ===
2025-07-29 11:38:49,302 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 11:38:49,598 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx，耗時296.9ms
2025-07-29 11:38:49,599 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\GMT_G2304_clean_converted.xlsx
2025-07-29 11:38:59,789 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-29 11:38:59,790 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_27pckdld
2025-07-29 11:38:59,791 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-29 11:38:59,795 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_27pckdld\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:39:01,314 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_27pckdld
2025-07-29 11:39:05,819 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-29 11:39:05,820 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_eo2zk9e0
2025-07-29 11:39:05,821 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-29 11:39:05,825 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_eo2zk9e0\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:39:06,277 - INFO - 開始完整轉換...
2025-07-29 11:39:06,277 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_eo2zk9e0\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:39:06,361 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-29 11:39:06,362 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-29 11:39:06,363 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 11:39:06,363 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-29 11:39:06,363 - INFO - 執行CTA到TMT格式轉換...
2025-07-29 11:39:06,967 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時602.3ms
2025-07-29 11:39:07,285 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時315.9ms
2025-07-29 11:39:07,291 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時5.0ms
2025-07-29 11:39:07,292 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時927.4ms
2025-07-29 11:39:07,292 - INFO - 應用CTA8280轉換處理...
2025-07-29 11:39:07,292 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-29 11:39:07,293 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-29 11:39:07,296 - INFO - 找到標題行在第2行
2025-07-29 11:39:07,296 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 3.7ms
2025-07-29 11:39:07,297 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-29 11:39:07,297 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-29 11:39:07,297 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-29 11:39:07,309 - INFO - myFindedRowN=2, myColumnN=14
2025-07-29 11:39:08,292 - INFO - ⚡ 超級批量插入6行完成，耗時137.8ms
2025-07-29 11:39:08,292 - INFO - 插入了6行在最前面
2025-07-29 11:39:08,293 - INFO - ⚡ 標題行插入優化完成，耗時138.4ms
2025-07-29 11:39:08,293 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 995.7ms
2025-07-29 11:39:08,293 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-29 11:39:08,294 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-29 11:39:08,294 - INFO - 清空位置 A8: "Index_No"
2025-07-29 11:39:08,294 - INFO - 清空位置 B8: "Dut_No"
2025-07-29 11:39:08,295 - INFO - 清空位置 A10: "Max"
2025-07-29 11:39:08,295 - INFO - 清空位置 B10: "Max"
2025-07-29 11:39:08,295 - INFO - 清空位置 A11: "Min"
2025-07-29 11:39:08,295 - INFO - 清空位置 B11: "Min"
2025-07-29 11:39:08,296 - INFO - ✅ 清空了6個多餘資料位置
2025-07-29 11:39:08,313 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 19.4ms
2025-07-29 11:39:08,313 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-29 11:39:08,314 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: 1.1ms
2025-07-29 11:39:08,315 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-29 11:39:08,321 - INFO - 找到SW_Bin列在第6列
2025-07-29 11:39:08,327 - INFO - 處理了264個設備的數據
2025-07-29 11:39:08,327 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 12.6ms
2025-07-29 11:39:08,328 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 1.04s
2025-07-29 11:39:08,329 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-29 11:39:08,329 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-29 11:39:08,329 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 11:39:08,330 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 11:39:08,330 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 11:39:08,330 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 11:39:08,330 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 11:39:08,331 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 11:39:08,331 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 11:39:08,331 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 11:39:08,331 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 11:39:08,331 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 11:39:08,331 - INFO - 🔍 開始統一數據收集...
2025-07-29 11:39:08,332 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 11:39:10,032 - INFO - 收集項目數據: 606個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 11:39:10,032 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 11:39:11,322 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-29 11:39:11,323 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 11:39:11,328 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-29 11:39:11,329 - INFO - 收集Site數據: 最大Site號=1，實際Site數=1，264筆Site資料
2025-07-29 11:39:11,329 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 11:39:11,330 - INFO - 收集限制值: Max 593個, Min 593個
2025-07-29 11:39:11,330 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 11:39:11,332 - INFO - 設置第6行項目編號: 從第3列開始，共606個項目
2025-07-29 11:39:11,332 - INFO - ⏱️ 統一數據收集 執行時間: 3.00s
2025-07-29 11:39:11,332 - INFO - ✅ 統一數據收集完成: 項目606個, 設備264個, Site1個
2025-07-29 11:39:11,333 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 3.00s
2025-07-29 11:39:11,333 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 11:39:11,334 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 11:39:11,334 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 11:39:11,334 - INFO -   - 設置紅色字體標記
2025-07-29 11:39:11,350 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 17.1ms
2025-07-29 11:39:11,351 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 11:39:11,351 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 11:39:11,352 - INFO -   - 填充預設限制值
2025-07-29 11:39:11,352 - INFO -   - 設置紅色字體標記
2025-07-29 11:39:11,358 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 7.2ms
2025-07-29 11:39:11,359 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 11:39:11,359 - INFO -   - 驗證數據完整性
2025-07-29 11:39:11,359 - INFO -   - 準備統一數據摘要
2025-07-29 11:39:11,360 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 11:39:11,360 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 11:39:11,360 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 11:39:11,362 - INFO - 設備行14: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,364 - INFO - 設備行15: 失敗項目ISHDN, 使用Bin值353
2025-07-29 11:39:11,366 - INFO - 設備行17: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,369 - INFO - 設備行18: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,372 - INFO - 設備行21: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,374 - INFO - 設備行23: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-29 11:39:11,377 - INFO - 設備行25: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,378 - INFO - 設備行26: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,380 - INFO - 設備行28: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,381 - INFO - 設備行29: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,383 - INFO - 設備行31: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,386 - INFO - 設備行32: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,389 - INFO - 設備行34: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,390 - INFO - 設備行35: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,393 - INFO - 設備行38: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,394 - INFO - 設備行39: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,397 - INFO - 設備行42: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,399 - INFO - 設備行43: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,400 - INFO - 設備行44: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,403 - INFO - 設備行45: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,405 - INFO - 設備行47: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,406 - INFO - 設備行48: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,409 - INFO - 設備行51: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,413 - INFO - 設備行55: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,414 - INFO - 設備行56: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,417 - INFO - 設備行58: 失敗項目CP_MTP, 使用Bin值78
2025-07-29 11:39:11,421 - INFO - 設備行61: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,424 - INFO - 設備行64: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,426 - INFO - 設備行65: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,427 - INFO - 設備行66: 失敗項目IQ_min, 使用Bin值350
2025-07-29 11:39:11,428 - INFO - 設備行67: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,430 - INFO - 設備行69: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,434 - INFO - 設備行72: 失敗項目A0_IIL, 使用Bin值326
2025-07-29 11:39:11,438 - INFO - 設備行75: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,439 - INFO - 設備行76: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,441 - INFO - 設備行77: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,444 - INFO - 設備行80: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,447 - INFO - 設備行83: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,448 - INFO - 設備行84: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,450 - INFO - 設備行85: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,454 - INFO - 設備行88: 失敗項目iCh4_OVP_Aft, 使用Bin值231
2025-07-29 11:39:11,455 - INFO - 設備行89: 失敗項目ISHDN, 使用Bin值353
2025-07-29 11:39:11,457 - INFO - 設備行90: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,458 - INFO - 設備行91: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,461 - INFO - 設備行94: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,463 - INFO - 設備行96: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,464 - INFO - 設備行97: 失敗項目OS_Short, 使用Bin值40
2025-07-29 11:39:11,466 - INFO - 設備行98: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,469 - INFO - 設備行99: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,470 - INFO - 設備行100: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,472 - INFO - 設備行101: 失敗項目ILED1_PwmHi, 使用Bin值365
2025-07-29 11:39:11,473 - INFO - 設備行102: 失敗項目Min_Duty, 使用Bin值196
2025-07-29 11:39:11,476 - INFO - 設備行105: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,477 - INFO - 設備行106: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,478 - INFO - 設備行107: 失敗項目VREF_bf, 使用Bin值136
2025-07-29 11:39:11,479 - INFO - 設備行108: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,481 - INFO - 設備行109: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,482 - INFO - 設備行110: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,484 - INFO - 設備行111: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,487 - INFO - 設備行113: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-29 11:39:11,488 - INFO - 設備行114: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,489 - INFO - 設備行115: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,491 - INFO - 設備行116: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,492 - INFO - 設備行117: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,493 - INFO - 設備行118: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 11:39:11,494 - INFO - 設備行119: 失敗項目OS_Short, 使用Bin值40
2025-07-29 11:39:11,495 - INFO - 設備行120: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,497 - INFO - 設備行121: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,498 - INFO - 設備行122: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,501 - INFO - 設備行123: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,503 - INFO - 設備行124: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,504 - INFO - 設備行125: 失敗項目ATPG_test, 使用Bin值485
2025-07-29 11:39:11,506 - INFO - 設備行126: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 11:39:11,507 - INFO - 設備行127: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,510 - INFO - 設備行130: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-29 11:39:11,511 - INFO - 設備行131: 失敗項目OS_Short, 使用Bin值40
2025-07-29 11:39:11,512 - INFO - 設備行132: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,513 - INFO - 設備行133: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,515 - INFO - 設備行135: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,517 - INFO - 設備行136: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,519 - INFO - 設備行137: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,520 - INFO - 設備行138: 失敗項目CR_CH3, 使用Bin值64
2025-07-29 11:39:11,521 - INFO - 設備行139: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,523 - INFO - 設備行140: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,524 - INFO - 設備行141: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,525 - INFO - 設備行142: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,526 - INFO - 設備行143: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,529 - INFO - 設備行146: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,530 - INFO - 設備行147: 失敗項目iCh4_6mA_Bef, 使用Bin值143
2025-07-29 11:39:11,531 - INFO - 設備行148: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,534 - INFO - 設備行150: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,538 - INFO - 設備行153: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,539 - INFO - 設備行154: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,542 - INFO - 設備行157: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,543 - INFO - 設備行158: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,544 - INFO - 設備行159: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 11:39:11,545 - INFO - 設備行160: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,547 - INFO - 設備行161: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-29 11:39:11,550 - INFO - 設備行164: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-29 11:39:11,552 - INFO - 設備行165: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,555 - INFO - 設備行168: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,558 - INFO - 設備行170: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,560 - INFO - 設備行172: 失敗項目ISHDN, 使用Bin值353
2025-07-29 11:39:11,562 - INFO - 設備行174: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,564 - INFO - 設備行176: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,565 - INFO - 設備行177: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,568 - INFO - 設備行178: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,570 - INFO - 設備行179: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,574 - INFO - 設備行183: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,576 - INFO - 設備行184: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,577 - INFO - 設備行185: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,578 - INFO - 設備行186: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,580 - INFO - 設備行188: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,581 - INFO - 設備行189: 失敗項目CR_PWM, 使用Bin值71
2025-07-29 11:39:11,585 - INFO - 設備行192: 失敗項目ATPG_test, 使用Bin值485
2025-07-29 11:39:11,587 - INFO - 設備行193: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,589 - INFO - 設備行195: 失敗項目iCh3_6mA_Bef, 使用Bin值142
2025-07-29 11:39:11,590 - INFO - 設備行196: 失敗項目IOC_code10, 使用Bin值183
2025-07-29 11:39:11,591 - INFO - 設備行197: 失敗項目CR_CH2, 使用Bin值65
2025-07-29 11:39:11,594 - INFO - 設備行200: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,595 - INFO - 設備行201: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,598 - INFO - 設備行204: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,599 - INFO - 設備行205: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,602 - INFO - 設備行206: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,603 - INFO - 設備行207: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,605 - INFO - 設備行208: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,606 - INFO - 設備行209: 失敗項目iCh5_6mA_Bef, 使用Bin值144
2025-07-29 11:39:11,607 - INFO - 設備行210: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,611 - INFO - 設備行214: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,613 - INFO - 設備行215: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,615 - INFO - 設備行217: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,617 - INFO - 設備行219: 失敗項目iCh1_6mA_Bef, 使用Bin值140
2025-07-29 11:39:11,624 - INFO - 設備行225: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,625 - INFO - 設備行226: 失敗項目iCh2_6mA_Bef, 使用Bin值141
2025-07-29 11:39:11,627 - INFO - 設備行228: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,629 - INFO - 設備行229: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,630 - INFO - 設備行230: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,631 - INFO - 設備行231: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,633 - INFO - 設備行232: 失敗項目VoutUVP_HYS, 使用Bin值399
2025-07-29 11:39:11,637 - INFO - 設備行235: 失敗項目READ_0X1D_Aft, 使用Bin值261
2025-07-29 11:39:11,638 - INFO - 設備行236: 失敗項目VoutOVP_L2H, 使用Bin值392
2025-07-29 11:39:11,640 - INFO - 設備行237: 失敗項目ISHDN, 使用Bin值353
2025-07-29 11:39:11,642 - INFO - 設備行239: 失敗項目iCh6_6mA_Bef, 使用Bin值145
2025-07-29 11:39:11,644 - INFO - 設備行241: 失敗項目ATPG_test, 使用Bin值485
2025-07-29 11:39:11,645 - INFO - 設備行242: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,661 - INFO - 設備行259: 失敗項目iCh1_OVP_Aft, 使用Bin值228
2025-07-29 11:39:11,663 - INFO - 設備行261: 失敗項目MC_ISET, 使用Bin值179
2025-07-29 11:39:11,678 - INFO - 重新計算並更新了264個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 11:39:11,678 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 318.3ms
2025-07-29 11:39:11,678 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 3.35s
2025-07-29 11:39:11,679 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 11:39:11,679 - INFO - 應用Device2BinControl處理...
2025-07-29 11:39:11,679 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 11:39:11,680 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 11:39:11,680 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 11:39:11,680 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 11:39:11,680 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 11:39:11,680 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 11:39:11,681 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 11:39:11,695 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 11:39:11,695 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 14.8ms
2025-07-29 11:39:11,696 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 11:39:11,696 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-29 11:39:11,696 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 11:39:11,697 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 11:39:11,697 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:39:11,698 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:39:11,698 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:39:11,698 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:39:11,698 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 11:39:11,699 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-29 11:39:11,699 - INFO - 收集原始Bin值: 264個設備
2025-07-29 11:39:12,000 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-29 11:39:12,000 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-29 11:39:12,001 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 11:39:12,001 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 11:39:12,001 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 304.6ms
2025-07-29 11:39:12,002 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 11:39:12,002 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:39:12,002 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:39:12,002 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:39:12,003 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:39:12,003 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:39:12,003 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:39:13,183 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-29 11:39:13,183 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-29 11:39:13,183 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 1.18s
2025-07-29 11:39:13,184 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 1.50s
2025-07-29 11:39:13,184 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 11:39:13,185 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 11:39:13,185 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 11:39:13,185 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:39:13,186 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:39:13,186 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:39:13,186 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:39:13,186 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 11:39:13,187 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:39:13,187 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:39:13,187 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:39:13,188 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:39:13,188 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:39:13,188 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:39:13,188 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 11:39:13,189 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 11:39:13,189 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 11:39:13,189 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 11:39:13,189 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 11:39:13,189 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 11:39:13,189 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 11:39:13,204 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 11:39:13,204 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 14.9ms
2025-07-29 11:39:13,205 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 11:39:13,205 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-29 11:39:13,206 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 11:39:13,206 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 11:39:13,206 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:39:13,207 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:39:13,207 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:39:13,207 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:39:13,207 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 11:39:13,208 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-29 11:39:13,208 - INFO - 收集原始Bin值: 264個設備
2025-07-29 11:39:13,498 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-29 11:39:13,498 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-29 11:39:13,499 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=117, Fail=147, Yield=44.32%
2025-07-29 11:39:13,499 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_eo2zk9e0\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:39:13,505 - INFO - VBA 362-420行（修正版）：填充了26個測試項目的Bin數據，無重複
2025-07-29 11:39:13,506 - INFO - 創建Site統計完成: 最大Site號=1，實際有設備的Site數=1
2025-07-29 11:39:13,506 - INFO -   Site 1: 264個設備
2025-07-29 11:39:13,506 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-29 11:39:13,507 - INFO - 填充Site 1統計完成: 264個設備
2025-07-29 11:39:13,507 - INFO - Site統計填充完成: 1個Site
2025-07-29 11:39:13,507 - INFO - 設置 AutoFilter 範圍: A6:G32
2025-07-29 11:39:13,508 - INFO - 按 B 欄由大到小排序了 26 行資料
2025-07-29 11:39:13,508 - INFO - 自動調整D列寬度: 17.6 (基於最大內容長度: 13)
2025-07-29 11:39:13,508 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 11:39:13,508 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 11:39:13,509 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 302.7ms
2025-07-29 11:39:13,509 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 11:39:13,509 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:39:13,509 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:39:13,510 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:39:13,510 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:39:13,510 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:39:13,510 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:39:15,606 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-29 11:39:15,607 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-29 11:39:15,607 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 2.10s
2025-07-29 11:39:15,607 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 2.42s
2025-07-29 11:39:15,608 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 11:39:15,608 - INFO - === 統一處理管道完成 ===
2025-07-29 11:39:19,106 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 11:39:20,201 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時1094.2ms
2025-07-29 11:39:20,201 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-29 11:39:30,416 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_eo2zk9e0
2025-07-29 11:40:15,804 - INFO - 開始解壓縮ZIP檔案: C:/Users/<USER>/Desktop/test/G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv.zip
2025-07-29 11:40:15,807 - INFO - 創建臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_3m1m7bel
2025-07-29 11:40:15,807 - INFO - ZIP檔案包含: ['G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv']
2025-07-29 11:40:15,812 - INFO - 成功解壓縮檔案: C:\Users\<USER>\AppData\Local\Temp\csv_converter_3m1m7bel\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:40:16,447 - INFO - 開始完整轉換...
2025-07-29 11:40:16,447 - INFO - 讀取CSV文件: C:\Users\<USER>\AppData\Local\Temp\csv_converter_3m1m7bel\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:40:16,543 - INFO - 成功讀取CSV文件，大小: (1543, 608)
2025-07-29 11:40:16,544 - INFO - 檢測到CTA8280測試儀格式（通過[Data]標記）
2025-07-29 11:40:16,545 - INFO - === 開始7步驟統一處理管道 ===
2025-07-29 11:40:16,545 - INFO - 步驟4: 檢測到CTA格式，需要執行步驟5轉換
2025-07-29 11:40:16,545 - INFO - 執行CTA到TMT格式轉換...
2025-07-29 11:40:17,164 - INFO - ⚡ 優化寫入完成: 1268行 × 608列，耗時616.9ms
2025-07-29 11:40:17,453 - INFO - ⚡ 優化寫入完成: 269行 × 608列，耗時287.1ms
2025-07-29 11:40:17,459 - INFO - ⚡ 優化寫入完成: 5行 × 608列，耗時5.0ms
2025-07-29 11:40:17,459 - INFO - ⚡ 優化工作簿創建完成: 3個工作表，耗時913.2ms
2025-07-29 11:40:17,460 - INFO - 應用CTA8280轉換處理...
2025-07-29 11:40:17,460 - INFO - 🚀 步驟5：開始CTA格式轉換為TMT格式...
2025-07-29 11:40:17,460 - INFO - 🔄 步驟1：開始找到標題行...
2025-07-29 11:40:17,464 - INFO - 找到標題行在第2行
2025-07-29 11:40:17,465 - INFO - ⏱️ 步驟1-找到標題行 執行時間: 4.2ms
2025-07-29 11:40:17,465 - INFO - 🔄 步驟2：開始刪除標題行之前的行...
2025-07-29 11:40:17,465 - INFO - ⏱️ 步驟2-刪除標題行之前的行 執行時間: <1ms
2025-07-29 11:40:17,466 - INFO - 🔄 步驟3：開始處理行列操作...
2025-07-29 11:40:17,477 - INFO - myFindedRowN=2, myColumnN=14
2025-07-29 11:40:18,437 - INFO - ⚡ 超級批量插入6行完成，耗時139.9ms
2025-07-29 11:40:18,438 - INFO - 插入了6行在最前面
2025-07-29 11:40:18,438 - INFO - ⚡ 標題行插入優化完成，耗時140.5ms
2025-07-29 11:40:18,439 - INFO - ⏱️ 步驟3-處理行列操作 執行時間: 972.6ms
2025-07-29 11:40:18,439 - INFO - 🔄 步驟4：開始設置CTA8280標準標題...
2025-07-29 11:40:18,439 - INFO - 🧹 開始清空CSV檔案的多餘資料...
2025-07-29 11:40:18,440 - INFO - 清空位置 A8: "Index_No"
2025-07-29 11:40:18,440 - INFO - 清空位置 B8: "Dut_No"
2025-07-29 11:40:18,440 - INFO - 清空位置 A10: "Max"
2025-07-29 11:40:18,440 - INFO - 清空位置 B10: "Max"
2025-07-29 11:40:18,441 - INFO - 清空位置 A11: "Min"
2025-07-29 11:40:18,441 - INFO - 清空位置 B11: "Min"
2025-07-29 11:40:18,441 - INFO - ✅ 清空了6個多餘資料位置
2025-07-29 11:40:18,458 - INFO - ⏱️ 步驟4-設置CTA8280標準標題 執行時間: 19.2ms
2025-07-29 11:40:18,459 - INFO - 🔄 步驟5：開始提取sum工作表信息...
2025-07-29 11:40:18,460 - INFO - ⏱️ 步驟5-提取sum工作表信息 執行時間: <1ms
2025-07-29 11:40:18,460 - INFO - 🔄 步驟6：開始生成Serial#和Bin#...
2025-07-29 11:40:18,465 - INFO - 找到SW_Bin列在第6列
2025-07-29 11:40:18,469 - INFO - 處理了264個設備的數據
2025-07-29 11:40:18,470 - INFO - ⏱️ 步驟6-生成Serial#和Bin# 執行時間: 9.9ms
2025-07-29 11:40:18,470 - INFO - ⏱️ 步驟5 CTA轉TMT格式轉換總時間 執行時間: 1.01s
2025-07-29 11:40:18,470 - INFO - ✅ 步驟5：CTA轉TMT格式轉換完成
2025-07-29 11:40:18,471 - INFO - 步驟5：在9A位置標記原始格式為CTA...
2025-07-29 11:40:18,471 - INFO - 🚀 步驟6: 開始數據填充與統一收集...
2025-07-29 11:40:18,471 - INFO - 🔄 步驟6.1: 開始統一數據收集...
2025-07-29 11:40:18,472 - INFO -   - 6.1.1 收集測試項目數據（項目數量、名稱、位置）
2025-07-29 11:40:18,472 - INFO -   - 6.1.2 收集設備數據（設備數量、行位置、Bin值）
2025-07-29 11:40:18,472 - INFO -   - 6.1.3 收集Site信息（Site列位置、Site數據、統計）
2025-07-29 11:40:18,473 - INFO -   - 6.1.4 收集限制值（Max/Min值）
2025-07-29 11:40:18,473 - INFO -   - 6.1.5 設置第6行項目編號
2025-07-29 11:40:18,474 - INFO - 應用FillEmptyItemName處理（統一優化）...
2025-07-29 11:40:18,474 - INFO - 🚀 步驟6：開始數據填充處理（統一優化版）...
2025-07-29 11:40:18,474 - INFO - 🔄 步驟1：開始統一數據收集...
2025-07-29 11:40:18,474 - INFO - 🔍 開始統一數據收集...
2025-07-29 11:40:18,475 - INFO - 🔄 6.1.1 收集測試項目數據...
2025-07-29 11:40:20,174 - INFO - 收集項目數據: 606個項目，包含第6行項目編號和MAX/MIN值的列
2025-07-29 11:40:20,175 - INFO - 🔄 6.1.2 收集設備數據...
2025-07-29 11:40:21,443 - INFO - 收集設備數據: 264個設備（已排除無測試數據的行）
2025-07-29 11:40:21,443 - INFO - 🔄 6.1.3 收集Site信息...
2025-07-29 11:40:21,448 - INFO - 找到Site列: 第8行第4列，標題: Site_No
2025-07-29 11:40:21,449 - INFO - 收集Site數據: 最大Site號=1，實際Site數=1，264筆Site資料
2025-07-29 11:40:21,449 - INFO - 🔄 6.1.4 收集限制值...
2025-07-29 11:40:21,450 - INFO - 收集限制值: Max 593個, Min 593個
2025-07-29 11:40:21,450 - INFO - 🔄 6.1.5 設置第6行項目編號...
2025-07-29 11:40:21,451 - INFO - 設置第6行項目編號: 從第3列開始，共606個項目
2025-07-29 11:40:21,451 - INFO - ⏱️ 統一數據收集 執行時間: 2.98s
2025-07-29 11:40:21,451 - INFO - ✅ 統一數據收集完成: 項目606個, 設備264個, Site1個
2025-07-29 11:40:21,452 - INFO - ⏱️ 步驟1-統一數據收集 執行時間: 2.98s
2025-07-29 11:40:21,452 - INFO - 🔄 6.2 填充測試項目名稱和編號...
2025-07-29 11:40:21,452 - INFO -   - 檢查第7行、第8行是否有項目信息
2025-07-29 11:40:21,453 - INFO -   - 填充缺失的項目編號和名稱
2025-07-29 11:40:21,453 - INFO -   - 設置紅色字體標記
2025-07-29 11:40:21,475 - INFO - ⏱️ 6.2-填充測試項目名稱和編號 執行時間: 22.8ms
2025-07-29 11:40:21,475 - INFO - 🔄 6.3 填充Min/Max值...
2025-07-29 11:40:21,476 - INFO -   - 檢查第10行（Max值）和第11行（Min值）
2025-07-29 11:40:21,477 - INFO -   - 填充預設限制值
2025-07-29 11:40:21,477 - INFO -   - 設置紅色字體標記
2025-07-29 11:40:21,485 - INFO - ⏱️ 6.3-填充Min/Max值 執行時間: 9.4ms
2025-07-29 11:40:21,485 - INFO - 🔄 6.4 數據驗證與整理...
2025-07-29 11:40:21,486 - INFO -   - 驗證數據完整性
2025-07-29 11:40:21,486 - INFO -   - 準備統一數據摘要
2025-07-29 11:40:21,486 - INFO -   - 重新計算並更新Data11分頁B列Bin數值
2025-07-29 11:40:21,487 - INFO - 🔄 6.5 重新計算並更新Data11分頁B列Bin數值...
2025-07-29 11:40:21,487 - INFO - 開始重新計算設備Bin值（使用第6行項目特定Bin值）...
2025-07-29 11:40:21,695 - INFO - 重新計算並更新了264個設備的Bin值（使用第6行項目特定Bin值）
2025-07-29 11:40:21,696 - INFO - ⏱️ 6.5-重新計算並更新Bin數值 執行時間: 208.9ms
2025-07-29 11:40:21,696 - INFO - ⏱️ 步驟6 數據填充與統一收集總時間 執行時間: 3.22s
2025-07-29 11:40:21,696 - INFO - ✅ 步驟6：數據填充與統一收集完成，統一數據摘要已準備好供步驟7使用
2025-07-29 11:40:21,697 - INFO - 應用Device2BinControl處理...
2025-07-29 11:40:21,697 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 11:40:21,697 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 11:40:21,698 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 11:40:21,698 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 11:40:21,698 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 11:40:21,698 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 11:40:21,699 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 11:40:21,711 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 11:40:21,712 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 13.2ms
2025-07-29 11:40:21,712 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 11:40:21,713 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-29 11:40:21,713 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 11:40:21,714 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 11:40:21,714 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:40:21,714 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:40:21,715 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:40:21,715 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:40:21,715 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 11:40:21,716 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-29 11:40:21,716 - INFO - 收集原始Bin值: 264個設備
2025-07-29 11:40:21,987 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-29 11:40:21,987 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-29 11:40:21,988 - INFO - ⏭️ 跳過Summary工作表創建（未要求創建）
2025-07-29 11:40:21,988 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 11:40:21,988 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 274.8ms
2025-07-29 11:40:21,989 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 11:40:21,989 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:40:21,989 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:40:21,989 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:40:21,989 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:40:21,990 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:40:21,990 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:40:23,133 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-29 11:40:23,133 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-29 11:40:23,134 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 1.15s
2025-07-29 11:40:23,134 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 1.44s
2025-07-29 11:40:23,134 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 11:40:23,134 - INFO - 🚀 步驟7: 開始Summary工作表生成...
2025-07-29 11:40:23,135 - INFO - 🔄 步驟7.1: 開始VBA核心分析（統一數據版）...
2025-07-29 11:40:23,135 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:40:23,135 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:40:23,135 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:40:23,135 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:40:23,136 - INFO - 🔄 步驟7.2: 開始創建增強Summary工作表...
2025-07-29 11:40:23,136 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:40:23,136 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:40:23,136 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:40:23,136 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:40:23,136 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:40:23,136 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:40:23,137 - INFO - 🔄 步驟1：開始工作表格式驗證...
2025-07-29 11:40:23,137 - INFO - ⏱️ 步驟1-工作表格式驗證 執行時間: <1ms
2025-07-29 11:40:23,137 - INFO - 🔄 步驟2：開始獲取原始格式類型...
2025-07-29 11:40:23,137 - INFO - 步驟7：讀取步驟5設定的原始格式標記: CTA
2025-07-29 11:40:23,137 - INFO - ⏱️ 步驟2-獲取原始格式類型 執行時間: <1ms
2025-07-29 11:40:23,137 - INFO - ✅ 步驟2：原始格式類型: CTA
2025-07-29 11:40:23,137 - INFO - 🔄 步驟3：開始Excel格式設置...
2025-07-29 11:40:23,151 - INFO - Excel格式設置完成：凍結窗格C13，自動篩選第12行
2025-07-29 11:40:23,152 - INFO - ⏱️ 步驟3-Excel格式設置 執行時間: 14.3ms
2025-07-29 11:40:23,152 - INFO - 🔄 步驟4：使用統一數據摘要...
2025-07-29 11:40:23,152 - INFO - ✅ 數據摘要: 項目606個, 設備264個, Site1個
2025-07-29 11:40:23,153 - INFO - ⏱️ 步驟4-使用統一數據摘要 執行時間: <1ms
2025-07-29 11:40:23,153 - INFO - 🔄 7.1 開始VBA核心分析（統一數據版）...
2025-07-29 11:40:23,153 - INFO -   - 7.1.1 收集原始Bin值
2025-07-29 11:40:23,154 - INFO -   - 7.1.2 執行設備分析（測試項目失敗檢測）
2025-07-29 11:40:23,154 - INFO -   - 7.1.3 計算Bin統計
2025-07-29 11:40:23,154 - INFO -   - 7.1.4 應用染色邏輯（Excel格式）
2025-07-29 11:40:23,155 - INFO - 開始VBA核心分析（統一數據完整版）...
2025-07-29 11:40:23,155 - INFO - 使用統一數據: 設備264個, 項目606個
2025-07-29 11:40:23,156 - INFO - 收集原始Bin值: 264個設備
2025-07-29 11:40:23,421 - INFO - 應用染色邏輯: 147個設備的失敗項目
2025-07-29 11:40:23,422 - INFO - Bin統計: Pass=117, Fail=147, Total=264
2025-07-29 11:40:23,422 - INFO - VBA 457-469行：填充基本統計 Total=264, Pass=117, Fail=147, Yield=44.32%
2025-07-29 11:40:23,423 - INFO - 添加原始檔案超連結: C:\Users\<USER>\AppData\Local\Temp\csv_converter_3m1m7bel\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328.csv
2025-07-29 11:40:23,429 - INFO - VBA 362-420行（修正版）：填充了26個測試項目的Bin數據，無重複
2025-07-29 11:40:23,429 - INFO - 創建Site統計完成: 最大Site號=1，實際有設備的Site數=1
2025-07-29 11:40:23,430 - INFO -   Site 1: 264個設備
2025-07-29 11:40:23,430 - INFO - 開始填充Site統計: 1個Site, 實際項目數量: 0
2025-07-29 11:40:23,430 - INFO - 填充Site 1統計完成: 264個設備
2025-07-29 11:40:23,431 - INFO - Site統計填充完成: 1個Site
2025-07-29 11:40:23,431 - INFO - 設置 AutoFilter 範圍: A6:G32
2025-07-29 11:40:23,433 - INFO - 按 B 欄由大到小排序了 26 行資料
2025-07-29 11:40:23,433 - INFO - 自動調整D列寬度: 17.6 (基於最大內容長度: 13)
2025-07-29 11:40:23,434 - INFO - 增強Summary工作表創建完成（統一數據版）
2025-07-29 11:40:23,434 - INFO - VBA核心分析完成（統一數據完整版）
2025-07-29 11:40:23,435 - INFO - ⏱️ 7.1-VBA核心分析 執行時間: 281.7ms
2025-07-29 11:40:23,435 - INFO - 🔄 7.2 開始創建增強Summary工作表...
2025-07-29 11:40:23,435 - INFO -   - 7.2.1 填充A、B列基本統計（Total、Pass、Fail、Yield）
2025-07-29 11:40:23,436 - INFO -   - 7.2.2 添加原始檔案超連結（C1）
2025-07-29 11:40:23,436 - INFO -   - 7.2.3 設置標題行（第6行：Bin、Count、%、Definition、Note）
2025-07-29 11:40:23,437 - INFO -   - 7.2.4 填充F列開始的Site統計（數量和百分比）
2025-07-29 11:40:23,437 - INFO -   - 7.2.5 填充測試項目Bin數據
2025-07-29 11:40:23,437 - INFO -   - 7.2.6 設置AutoFilter和排序
2025-07-29 11:40:25,424 - INFO - ⚡ 超級批量字體設置完成，總共處理159295個cell
2025-07-29 11:40:25,424 - INFO - 設置數據區域字體顏色: 行13-276, 列2-608
2025-07-29 11:40:25,425 - INFO - ⏱️ 7.2-創建增強Summary工作表 執行時間: 1.99s
2025-07-29 11:40:25,425 - INFO - ⏱️ 步驟7 Summary工作表生成總時間 執行時間: 2.29s
2025-07-29 11:40:25,425 - INFO - ✅ 步驟7：Summary工作表生成完成
2025-07-29 11:40:25,426 - INFO - === 統一處理管道完成 ===
2025-07-29 11:40:28,909 - INFO - ⚡ 工作簿保存前優化設置完成
2025-07-29 11:40:29,967 - INFO - ⚡ 超級優化保存完成: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx，耗時1058.3ms
2025-07-29 11:40:29,968 - INFO - 完整轉換完成！輸出文件: C:\Users\<USER>\Desktop\test\G2747UP1U-9_Q61C40.12_F2550620A_FT1_R1_F_20250718110328_clean_converted.xlsx
2025-07-29 11:40:35,645 - INFO - 清理臨時目錄: C:\Users\<USER>\AppData\Local\Temp\csv_converter_3m1m7bel
