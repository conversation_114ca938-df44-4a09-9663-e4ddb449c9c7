#!/usr/bin/env python3
"""
處理器模組包
"""

from .base_processor import BaseProcessor
from .cta8280_processor import CTA8280Processor
from .fill_empty_processor import FillEmptyItemNameProcessor
from .device_bin_processor import Device2BinControlProcessor

# 工廠函數 - 保持向後兼容性
def get_cta8280_processor():
    """獲取CTA8280處理器實例"""
    return CTA8280Processor()

def get_fill_empty_processor():
    """獲取FillEmptyItemName處理器實例"""
    return FillEmptyItemNameProcessor()

def get_device2bin_processor():
    """獲取Device2BinControl處理器實例"""
    return Device2BinControlProcessor()

__all__ = [
    'BaseProcessor',
    'CTA8280Processor',
    'FillEmptyItemNameProcessor',
    'Device2BinControlProcessor',
    'get_cta8280_processor',
    'get_fill_empty_processor',
    'get_device2bin_processor'
]
