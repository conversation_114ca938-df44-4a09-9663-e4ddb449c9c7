# 🔧 安裝依賴模組

## 問題說明
執行 `python main.py` 時出現錯誤：
```
ModuleNotFoundError: No module named 'pandas'
```

## 解決方案

### 方法1：使用pip安裝（推薦）
```bash
pip install pandas openpyxl
```

### 方法2：使用requirements.txt安裝
```bash
pip install -r requirements.txt
```

### 方法3：逐個安裝
```bash
pip install pandas>=1.3.0
pip install openpyxl>=3.0.0
```

## 必要模組說明

### pandas
- **用途**：數據處理和CSV檔案讀取
- **版本**：>=1.3.0
- **功能**：
  - 讀取CSV檔案為DataFrame
  - 數據格式檢測（TMT vs CTA8280）
  - 數據分割和處理

### openpyxl
- **用途**：Excel檔案讀寫
- **版本**：>=3.0.0
- **功能**：
  - 創建Excel工作簿
  - 寫入數據到工作表
  - 設置格式和樣式

## 安裝後測試
安裝完成後，再次執行：
```bash
python main.py
```

應該可以正常啟動GUI界面。

## 如果仍有問題
如果安裝後仍有問題，請檢查：
1. Python版本是否>=3.7
2. pip是否為最新版本：`pip install --upgrade pip`
3. 是否在正確的Python環境中安裝
